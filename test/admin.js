const { expect, assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const moment = require('moment');
const { DateTime } = require('luxon');
const {
  app, validImagePath, initSocket, destroySocket, getSocketPromise, waitMs,
} = require('./common');
const { notifs, reset, waitFor, setReplicateMockResponse } = require('./stub');
const User = require('../models/user');
const PhoneUpdateLogs = require('../models/phone-update-logs');
const Report = require('../models/report');
const Question = require('../models/question');
const Comment = require('../models/comment');
const Interest = require('../models/interest');
const BannedSource = require('../models/banned-source');
const BannedUser = require('../models/banned-user');
const interestLib = require('../lib/interest');
const userLib = require('../lib/user');
const { pageSize } = require('../lib/constants');
const constants = require('../lib/constants');
const { getMockEmail, getMockNumber } = require('./utilities/mockuser');
const { fakeAdminMessaging } = require('./stub');
const { admin } = require('../config/firebase-admin');
const QuestionCandidate = require('../models/question-candidate');
const Translation = require('../models/translation');
const { BOO_SUPPORT_ID, BOO_BOT_ID } = require('../lib/chat');
const Subcategory = require('../models/subcategory');
const Category = require('../models/category');
const CoinTransaction = require('../models/coin-transaction');
const Profile = require('../models/profile');
const databaseLib = require('../lib/database');
const { createQuestion } = require('../lib/social');
const { initApp, setFcmToken, fetchCoinData, postQuestion, postComment, getMyProfile,
} = require('./helper/api');
const PoseVerification = require('../models/pose-verification');
const iapHelper = require('./helper/iap');
const CoinPurchaseReceipt = require('../models/coin-purchase-receipt');
const Chat = require('../models/chat');

async function createSupportUser(supportId = BOO_SUPPORT_ID) {
  const res = await request(app)
    .get('/v1/user')
    .set('authorization', supportId);
  expect(res.status).to.equal(200);
}

describe('search and delete qod', async () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 4; uid++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }
    const user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { support: true, approveQod: true };
    await user.save();

    await createSupportUser();
  });

  it('test /questions/byUser route', async () => {
    const totalQuestions = 15;
    let totalApprovedQuestions = 0;
    for (let i = 0; i < totalQuestions; i++) {
      let res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: `boo qod ${i}`,
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);
    }

    let res = await request(app)
      .get('/v1/admin/questionCandidates')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.eql(200);
    const candidates = res.body.candidates;

    res = await request(app)
      .post('/v1/admin/questionCandidates/status')
      .set('authorization', 0)
      .send({
        id: candidates[0].id,
        status: 'approved',
      });
    expect(res.status).to.eql(200);
    ++totalApprovedQuestions;

    // should return all 15
    res = await request(app)
      .get('/v1/admin/qods/byUser')
      .set('authorization', 0)
      .query({ user: 1 });
    expect(res.status).to.eql(200);
    expect(res.body.qods.length).to.equal(1);
    expect(res.body.questionCandidates.length).to.equal(totalQuestions - 1);

    // approve 5 more candidates to
    const limit = 5;
    const qodCandidates = await QuestionCandidate.find({ createdBy: '1', status: 'pending' }).limit(limit);
    for (const candidate of qodCandidates) {
      await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: candidate._id,
          status: 'approved',
        });
      ++totalApprovedQuestions;
    }

    const qodKeys = ['_id', 'createdAt', 'text', 'language'];
    const candidateKeys = ['_id', 'createdAt', 'text', 'language', 'isAnonymous'];
    res = await request(app)
      .get('/v1/admin/qods/byUser')
      .set('authorization', 0)
      .query({ user: 1 });
    expect(res.status).to.eql(200);
    // eslint-disable-next-line no-prototype-builtins
    expect(res.body.qods.every(qod => qodKeys.every(key => qod.hasOwnProperty(key)))).to.equal(true);
    // eslint-disable-next-line no-prototype-builtins
    expect(res.body.questionCandidates.every(candidate => candidateKeys.every(key => candidate.hasOwnProperty(key)))).to.equal(true);
    expect(res.body.qods.length).to.equal(totalApprovedQuestions);
    expect(res.body.questionCandidates.length).to.equal(totalQuestions - totalApprovedQuestions);

    // no user id
    res = await request(app)
      .get('/v1/admin/qods/byUser')
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    // user 1 does not have admin permissions
    res = await request(app)
      .get('/v1/admin/qods/byUser')
      .set('authorization', 1)
      .query({ user: 2 });
    expect(res.status).to.equal(403);

    // user 2 does not have any qod or candidates
    res = await request(app)
      .get('/v1/admin/qods/byUser')
      .set('authorization', 0)
      .query({ user: 2 });
    expect(res.status).to.equal(200);
    expect(res.body.qods.length).to.equal(0);
    expect(res.body.questionCandidates.length).to.equal(0);
  });

  it('test qod delete route', async () => {
    for (let i = 0; i < 5; i++) {
      const res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: `boo qod ${i}`,
          isAnonymous: false,
          language: 'bn',
        });
      expect(res.status).to.eql(200);
    }

    // Approve all QOD candidates
    const candidates = await QuestionCandidate.find({ createdBy: '1', status: 'pending' });
    for (const candidate of candidates) {
      const res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: candidate._id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);
    }

    // Retrieve all QODs
    const questionOfTheDays = await Question.find({ interestName: 'questions' }).sort({ createdAt: -1 }).lean();
    expect(questionOfTheDays.length).to.equal(5);
    const { _id: latestQuestionId, createdAt: latestQuestionCreatedAt } = questionOfTheDays[0];
    // Adjust the creation date of one QOD to yesterday
    await Question.updateOne({ _id: questionOfTheDays[2]._id }, { $set: { createdAt: moment().subtract(1, 'minute').toDate() } });

    // Test case: No QOD ID provided
    let res = await request(app)
      .delete(`/v1/admin/qod`)
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    // Test case: Invalid QOD ID provided
    res = await request(app)
      .delete(`/v1/admin/qod`)
      .set('authorization', 0)
      .query({ questionId: 'random' });
    expect(res.status).to.equal(422);

    // Test case: prior QOD should be changed to anonymous instead of deleted
    res = await request(app)
      .delete(`/v1/admin/qod`)
      .set('authorization', 0)
      .query({ questionId: questionOfTheDays[2]?._id?.toString() });
    expect(res.status).to.equal(200);

    updatedQODList = await Question.find({ language: questionOfTheDays[2].language, interestName: 'questions' }).sort({ createdAt: -1 }).lean();
    expect(updatedQODList.length).to.equal(5);
    expect(updatedQODList[4].createdBy).to.equal(null);
    expect(updatedQODList[3].createdBy).to.equal('1');

    // Test case: Delete latest QOD but should not update any QOD's createdAt
    res = await request(app)
      .delete(`/v1/admin/qod`)
      .set('authorization', 0)
      .query({ questionId: latestQuestionId.toString() });
    expect(res.status).to.equal(200);

    updatedQODList = await Question.find({ language: questionOfTheDays[2].language, interestName: 'questions' }).sort({ createdAt: -1 }).lean();
    expect(updatedQODList[0]._id).to.not.equal(latestQuestionId);
    expect(updatedQODList[0].createdAt.toISOString()).to.not.equal(latestQuestionCreatedAt.toISOString());
    const penultimateQOD = updatedQODList[1];

    // Test case: Delete second latest QOD and update latest QOD's createdAt
    res = await request(app)
      .delete(`/v1/admin/qod`)
      .set('authorization', 0)
      .query({ questionId: penultimateQOD._id.toString() });
    expect(res.status).to.equal(200);

    updatedQODList = await Question.find({ language: penultimateQOD.language, interestName: 'questions' }).sort({ createdAt: -1 }).lean();
    expect(updatedQODList.length).to.equal(3);
    expect(updatedQODList[0]._id).to.not.equal(penultimateQOD._id);
    expect(updatedQODList[0].createdAt.toISOString()).to.equal(penultimateQOD.createdAt.toISOString());
  });

  it('test questionCandidate delete route', async () => {
    for (let i = 0; i < 2; i++) {
      let res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: `boo qod ${i}`,
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);
    }
    let candidates = await QuestionCandidate.find({ status: 'pending' }).sort({ createdAt: -1 }).lean();

    // should delete a pending candidate
    let res = await request(app)
      .delete(`/v1/admin/questionCandidate`)
      .set('authorization', 0)
      .query({ questionId: candidates[0]._id.toString() });
    expect(res.status).to.equal(200);

    // No question id
    res = await request(app)
      .delete(`/v1/admin/questionCandidate`)
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    // invalid question id
    res = await request(app)
      .delete(`/v1/admin/questionCandidate`)
      .set('authorization', 0)
      .query({ questionId: 'invalid' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .post('/v1/admin/questionCandidates/status')
      .set('authorization', 0)
      .send({
        id: candidates[1]._id,
        status: 'approved',
      });
    expect(res.status).to.eql(200);

    // should not be able to delete a approved candidate
    res = await request(app)
      .delete(`/v1/admin/questionCandidate`)
      .set('authorization', 0)
      .query({ questionId: candidates[1]._id.toString() });
    expect(res.status).to.equal(404);
  });
});

describe('admin', () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app)
        .get('/v1/user')
        .set('authorization', uid);
      expect(res.status).to.equal(200);
    }
    const user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();
  });

  it('test qod message sender', async () => {
    await createSupportUser();
    await createSupportUser(BOO_BOT_ID);

    // User 1 with no app version
    let res = await request(app)
      .post('/v1/question/submitQod')
      .set('authorization', 1)
      .send({
        text: `boo qod by user 1`,
        isAnonymous: false,
      });
    expect(res.status).to.eql(200);

    // message should be sent by support user
    let chat = await Chat.findDirectChat('1', BOO_SUPPORT_ID, true);
    expect(chat).to.not.equal(null);
    expect(chat.lastMessage.text).to.equal('Thank you for sending your question! You will be notified if selected.');

    res = await request(app)
      .get('/v1/admin/questionCandidates')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.eql(200);
    let candidates = res.body.candidates;

    res = await request(app)
      .post('/v1/admin/questionCandidates/status')
      .set('authorization', 0)
      .send({
        id: candidates[0].id,
        status: 'approved',
      });
    expect(res.status).to.eql(200);

    chat = await Chat.findDirectChat('1', BOO_SUPPORT_ID, true);
    expect(chat.lastMessage.text).to.match(/^Your question ".*?", has been scheduled for .+$/);

    // create user 2 with app version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.70' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question/submitQod')
      .set('authorization', 2)
      .send({
        text: `boo qod by user 2`,
        isAnonymous: false,
      });
    expect(res.status).to.eql(200);

    chat = await Chat.findDirectChat('2', BOO_SUPPORT_ID, true);
    expect(chat).to.equal(null);

    chat = await Chat.findDirectChat('2', BOO_BOT_ID, true);
    expect(chat).to.not.equal(null);
    expect(chat.lastMessage.text).to.equal('Thank you for sending your question! You will be notified if selected.');

    res = await request(app)
      .get('/v1/admin/questionCandidates')
      .set('authorization', 0)
      .send({});
    expect(res.status).to.eql(200);
    candidates = res.body.candidates;

    res = await request(app)
      .post('/v1/admin/questionCandidates/status')
      .set('authorization', 0)
      .send({
        id: candidates[0].id,
        status: 'approved',
      });
    expect(res.status).to.eql(200);

    chat = await Chat.findDirectChat('2', BOO_BOT_ID, true);
    expect(chat.lastMessage.text).to.match(/^Your question ".*?", has been scheduled for .+$/);
  });

  it('should test /v1/admin/receipts route', async () => {
    let res = await request(app)
      .get('/v1/user')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    // No support permission
    res = await request(app)
      .get('/v1/admin/receipts')
      .set('authorization', 3);
    expect(res.status).to.equal(403);

    // No transactionId
    res = await request(app)
      .get('/v1/admin/receipts')
      .set('authorization', 0);
    expect(res.status).to.equal(422);

    // Invalid transactionId
    res = await request(app)
      .get('/v1/admin/receipts')
      .set('authorization', 0)
      .query({ transactionId: 'random' });
    expect(res.status).to.equal(404);

    let receipt = iapHelper.getValidGoogleReceipt('1000_coins', Date.now());
    res = await request(app)
      .put('/v1/coins/purchaseCoins')
      .set('authorization', 3)
      .send({
        receipt,
      });
    expect(res.status).to.equal(200);

    const record = await CoinPurchaseReceipt.findOne({ user: '3' });

    res = await request(app)
      .get('/v1/admin/receipts')
      .set('authorization', 0)
      .query({ transactionId: record.transactionId });
    expect(res.status).to.equal(200);
    expect(res.body.user).to.equal('3');
    expect(new Date(res.body.purchaseDate).getTime()).to.equal(new Date(record.purchaseDate).getTime());
    expect(res.body.productId).to.equal(record.productId);
    expect(res.body.currency).to.equal(record.currency);
    expect(res.body.price).to.equal(record.price);
  });

  it('admins should have premium', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.premium).to.equal(true);
  });

  it('view handle even if not searchable', async () => {
    res = await request(app)
      .put('/v1/user/handle')
      .set('authorization', 1)
      .send({ handle: 'handle1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/searchable')
      .set('authorization', 1)
      .send({ searchable: false });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.handle).to.equal('handle1');
  });

  it('view posts even if hidden', async () => {
    res = await request(app)
      .put('/v1/user/hideQuestions')
      .set('authorization', 1)
      .send({ hideQuestions: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/hideComments')
      .set('authorization', 1)
      .send({ hideComments: true });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.hideQuestions).to.equal(false);
    expect(res.body.user.hideComments).to.equal(false);
  });

  describe('view coin transactions', () => {
    it('view user coin transactions', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 2);
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/coins')
        .set('authorization', 1);
      expect(res.status).to.equal(200);

      // claim an reward
      res = await request(app)
        .put('/v1/coins/detailedReview')
        .set('authorization', 1);
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/user/sendDirectMessage')
        .set('authorization', 1)
        .send({
          user: '2',
          message: 'Hi',
          price: 50,
        });
      expect(res.status).to.equal(200);

      // become premium and send dm for free - no record created
      user = await User.findOne({ _id: 1 });
      user.premiumExpiration = Date.now() + 86400000;
      await user.save();

      res = await request(app)
        .patch('/v1/user/sendDirectMessage')
        .set('authorization', 1)
        .send({
          user: '2',
          message: 'Hi',
          price: 0,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/admin/user/coinTransactions')
        .set('authorization', 0)
        .query({ user: '1' });
      expect(res.status).to.equal(200);
      console.log(res.body);
      // expecting the transaction numbers to be 3
      expect(res.body.transactions.length).to.equal(3);
      for (let i = 0; i < res.body.transactions.length; i++) {
        // expect the user to be user 1
        expect(res.body.transactions[i].user).to.equal('1');
        if (i !== res.body.transactions.length - 1) {
          // expect the date is sorted in descending order
          expect(Date.parse(res.body.transactions[i].createdAt)).to.be.above(Date.parse(res.body.transactions[i+1].createdAt));
        }
      }
    });

    describe('view user coin based on filter', () => {
      beforeEach(async () => {
        let newBalance = 0;
        // inject 15 data to database
        for (let i = 0; i < 15; i++) {
          newBalance += i;
          const createdAt = moment().add(i, 'days');
          await CoinTransaction.create({
            user: '1',
            transactionAmount: i,
            newBalance: newBalance + i,
            description: 'Test balance ' + i,
            createdAt: createdAt.format(),
          });
        }
      });

      it('should only show latest 10 records sorted desc', async () => {
        res = await request(app)
          .get('/v1/admin/user/coinTransactions')
          .set('authorization', 0)
          .query({ user: '1' });
        expect(res.status).to.equal(200);
        // expecting the transaction numbers to be 10
        expect(res.body.transactions.length).to.equal(10);
        for (let i = 0; i < res.body.transactions.length; i++) {
          // expect the user to be user 1
          expect(res.body.transactions[i].user).to.equal('1');
          if (i !== res.body.transactions.length - 1) {
            // expect the date is sorted in descending order
            expect(moment(res.body.transactions[i].createdAt).toDate()).to.be.above(moment(res.body.transactions[i+1].createdAt).toDate());
          }
        }
      });

      it('should only shows 10 records of transactions before the given date', async () => {
        const givenDate = moment().add(13, 'days');
        res = await request(app)
          .get('/v1/admin/user/coinTransactions?before=' + givenDate.format('YYYY-MM-DD'))
          .set('authorization', 0)
          .query({ user: '1' });
        expect(res.status).to.equal(200);
        // expecting the transaction numbers to be 10
        expect(res.body.transactions.length).to.equal(10);
        for (let i = 0; i < res.body.transactions.length; i++) {
          // expect the user to be user 1
          expect(res.body.transactions[i].user).to.equal('1');
          if (i !== res.body.transactions.length - 1) {
            // expect the date is sorted in descending order
            expect(moment(res.body.transactions[i].createdAt).toDate()).to.be.above(moment(res.body.transactions[i+1].createdAt).toDate());
          }
          // expect the date is before given date
          expect(moment(res.body.transactions[i].createdAt).toDate()).to.be.below(givenDate.toDate());
        }
      });
      it('should only shows 5 records of transactions when there are only 5 data created before the given date', async () => {
        const givenDate = moment().add(5, 'days');
        res = await request(app)
          .get('/v1/admin/user/coinTransactions?before=' + givenDate.format('YYYY-MM-DD'))
          .set('authorization', 0)
          .query({ user: '1' });
        expect(res.status).to.equal(200);
        // expecting the transaction numbers to be 5
        expect(res.body.transactions.length).to.equal(5);
        for (let i = 0; i < res.body.transactions.length; i++) {
          // expect the user to be user 1
          expect(res.body.transactions[i].user).to.equal('1');
          if (i !== res.body.transactions.length - 1) {
            // expect the date is sorted in descending order
            expect(moment(res.body.transactions[i].createdAt).toDate()).to.be.above(moment(res.body.transactions[i+1].createdAt).toDate());
          }
          // expect the date is before given date
          expect(moment(res.body.transactions[i].createdAt).toDate()).to.be.below(givenDate.toDate());
        }
      });

      it('should show no record when there given date is before the first trx for the user', async () => {
        const givenDate = moment().add(-1, 'days');
        res = await request(app)
          .get('/v1/admin/user/coinTransactions?before=' + givenDate.format('YYYY-MM-DD'))
          .set('authorization', 0)
          .query({ user: '1' });
        expect(res.status).to.equal(200);
        // expecting the transaction numbers to be 0
        expect(res.body.transactions.length).to.equal(0);
      });
    })
  });

  it('view posts even if inactive', async () => {
    user = await User.findById('1');
    user.updatedAt = new Date(2000, 1, 1);
    await user.save();

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.hideQuestions).to.equal(false);
    expect(res.body.user.hideComments).to.equal(false);
  });

  it('view posts of banned user', async () => {
    // user 1 creates question
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestId: kpopId,
        title: '1',
        text: '1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // ban user 1
    u = await User.findById('1');
    u.shadowBanned = true;
    await u.save();

    // admin can still see post when searching by user
    res = await request(app)
      .get('/v1/user/questions')
      .set('authorization', 0)
      .query({ createdBy: 1 });
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);
    expect(res.body.questions[0]._id).to.equal(q1Id);

    // admin does not see post on feed
    res = await request(app)
      .get('/v1/question/feed')
      .query({})
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('find same device id', async () => {
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.accountsWithSameDeviceId).to.eql([]);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ deviceId: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ deviceId: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.accountsWithSameDeviceId).to.eql(['2']);

    // ban account 2
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({ user: '2' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.accountsWithSameDeviceId).to.eql(['2']);

    // delete account 2
    res = await request(app)
      .post('/v1/user/accountDeletion')
      .set('authorization', 2)
      .send({
        reason: [1, 4],
        feedback: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 2 });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.accountsWithSameDeviceId).to.eql(['2']);
  });

  it('ban same device id', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ deviceId: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ deviceId: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ deviceId: '3' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(true);
    expect((await User.findOne({ _id: 2 })).shadowBanned).to.equal(true);
    expect((await User.findOne({ _id: 3 })).shadowBanned).to.equal(false);
  });

  it('view users who blocked', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    user = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    user = await request(app)
      .patch('/v1/user/block')
      .set('authorization', 1)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/usersWhoBlocked')
      .set('authorization', 0)
      .query({ user: '2' });
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(2);
    expect(res.body.users[0]._id).to.equal('1');
    expect(res.body.users[1]._id).to.equal('0');
  });

  it('admin level', async () => {
    // no support permissions
    user = await User.findOne({ _id: 0 });
    user.adminPermissions.all = undefined;
    user.adminPermissions.support = undefined;
    res = await user.save();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(403);

    // no permissions
    user = await User.findOne({ _id: 0 });
    user.adminPermissions = undefined;
    res = await user.save();

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(403);
  });

  it('admin bans user', async () => {
    // non-admin forbidden to ban
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(403);

    // try ban wrong id
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(404);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(false);

    // ban
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
        bannedReason: 'Scammer',
        bannedNotes: 'scammer',
      });
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 1 });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('Scammer');
    expect(user.bannedNotes).to.equal('scammer');
    expect(user.banHistory.length).to.equal(1);
    expect(user.banHistory[0].action).to.equal('ban');
    expect(user.banHistory[0].by).to.equal('0');
    expect(user.banHistory[0].reason).to.equal('Scammer');
    expect(user.banHistory[0].notes).to.equal('scammer');

    // check ban data
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.bannedProfileData.length).to.equal(1);
    expect(res.body.bannedProfileData[0].date).to.not.equal();
    expect(res.body.bannedProfileData[0].profile).to.eql(res.body.user);
    expect(res.body.deletedAccount).to.equal();

    // update banned notes
    res = await request(app)
      .put('/v1/admin/bannedNotes')
      .set('authorization', 0)
      .send({
        user: '1',
        bannedNotes: 'notes',
      });
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 1 });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedNotes).to.equal('notes');
    expect(user.banHistory.length).to.equal(2);
    expect(user.banHistory[1].action).to.equal('notes');
    expect(user.banHistory[1].by).to.equal('0');
    expect(user.banHistory[1].notes).to.equal('notes');

    // unban
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '1',
        notes: 'not scammer',
      });
    expect(res.status).to.equal(200);
    user = await User.findOne({ _id: 1 });
    expect(user.shadowBanned).to.equal(false);
    expect(user.banHistory.length).to.equal(3);
    expect(user.banHistory[2].action).to.equal('unban');
    expect(user.banHistory[2].by).to.equal('0');
    expect(user.banHistory[2].notes).to.equal('not scammer');
  });

  it('search deleted banned account', async () => {
    // edit description
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 1)
      .send({ description: '1' });
    expect(res.status).to.equal(200);

    // ban
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
        bannedReason: 'Scammer',
        bannedNotes: 'scammer',
      });
    expect(res.status).to.equal(200);

    // unban
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '1',
        notes: 'not scammer',
      });
    expect(res.status).to.equal(200);

    // edit description
    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 1)
      .send({ description: '2' });
    expect(res.status).to.equal(200);

    // ban
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
        bannedReason: 'Scammer',
        bannedNotes: 'scammer',
      });
    expect(res.status).to.equal(200);

    // delete account
    res = await request(app)
      .post('/v1/user/accountDeletion')
      .set('authorization', 1)
      .send({
        reason: [1, 4],
        feedback: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 1 });
    expect(res.status).to.equal(200);

    // search for deleted banned account
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal('2');
    expect(res.body.deletedAccount).to.equal(true);
    expect(res.body.bannedProfileData.length).to.equal(2);
  });

  it('admins are not allowed to delete account', async () => {
    res = await request(app)
      .post('/v1/user/accountDeletion')
      .set('authorization', 0)
      .send({
        reason: [1, 4],
        feedback: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 0 });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user).to.not.equal();
    expect(user.deletionRequestDate).to.equal();
    expect(user.adminAttemptedAccountDeletion).to.equal(true);
  });

  it('admin unbans deleted user', async () => {
    const payload = {
      img: 'base-64-image-data',
      secure: {
        version: "2.7.0",
        token: "token-data",
        verification: "verification-data",
        signature: "signature-data",
      },
    };
    let bannedFaces = [];

    fakeRekognition.indexFaces = function (params) {
      if (params.CollectionId === constants.UPDATED_BANNED_USERS_COLL_ID) {
        bannedFaces.push(params.Image.S3Object.Name);
      }

      const impl = function (resolve, reject) {
        resolve({
          FaceRecords: [{ Face: { FaceId: params.Image.S3Object.Name } }],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    fakeRekognition.deleteFaces = function (params) {
      const impl = function (resolve, reject) {
        bannedFaces = bannedFaces.filter((f) => !params.FaceIds.includes(f));
        resolve({
          DeletedFaces: [params.FaceIds],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ appVersion: '1.13.65' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 2)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // should verify the profile
    res = await request(app)
      .post('/v1/user/profileVerificationPicture/liveness')
      .set('authorization', 2)
      .send(payload);
    expect(res.status).to.equal(200);
    expect(res.body.verificationStatus).to.equal('verified');
    expect(res.body.rejectionReason).to.equal(undefined);

    // ban
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
        bannedReason: 'Scammer',
        bannedNotes: 'scammer',
      });
    expect(res.status).to.equal(200);

    let user = await User.findById('2');
    expect(user.shadowBanned).to.equal(true);

    let bannedUser = await BannedUser.findOne({ user: '2' });
    expect(bannedUser.faceIds.length).to.equal(1);

    let bannedSources = await BannedSource.find({});
    expect(bannedSources.length).to.equal(1);
    expect(bannedSources[0].user).to.equal('2');
    expect(bannedSources[0].sourceType).to.equal('email');
    expect(bannedSources[0].sourceVal).to.equal('<EMAIL>');

    // delete account
    res = await request(app)
      .post('/v1/user/accountDeletion')
      .set('authorization', 2)
      .send({
        reason: [1, 4],
        feedback: 'feedback',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 2 });
    expect(res.status).to.equal(200);

    // search for deleted banned account
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '2',
      });
    expect(res.status).to.equal(200);

    // unban deleted account
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({ user: '2', notes: 'not scammer' });
    expect(res.status).to.equal(200);

    // user should be unbanned
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '2',
      });
    expect(res.status).to.equal(200);
    expect(res.body.metadata.shadowBanned).to.equal(false);

    user = await User.findById('2');
    expect(user).to.equal(null);
    expect(bannedFaces.length).to.equal(0);

    bannedSources = await BannedSource.findOne({ user: '2' });
    expect(bannedSources).to.equal(null);
  });

  it('translator bans user', async () => {

    //make user 0 translator
    {
      const user = await User.findOne({ _id: 0 });
      user.adminPermissions = { translator: 'de' };
      await user.save();
    }

    //create User 2
    await initApp(2);

    //user 1 posts in language = "fr"
    res = await postQuestion(1, {
      interestName: 'kpop',
      title: '1',
      text: '1',
      language: 'fr',
    });

    let qId = res._id;

    //user 2 comments on question by user 1
    res = await postComment(2,
      {
        questionId: qId,
        text: 'Comment1',
        parentId: qId,
      });

    // translator forbidden to ban users with questions / comments in language other than 'de'
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(403);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(403);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(false);//user 1 not banned
    expect((await User.findOne({ _id: 2 })).shadowBanned).to.equal(false);//user 2 not banned

    //user 1 posts another question in language = "de"
    res = await postQuestion(1, {
      interestName: 'kpop',
      title: '2',
      text: '2',
      language: 'de',
    });
    qId = res._id;

    //user 2 comments on the question by user 1
    res = await postComment(2,
      {
        questionId: qId,
        text: 'Comment2',
        parentId: qId,
      });

    // translator bans user 2 successfully
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(false);//user 1 not banned
    expect((await User.findOne({ _id: 2 })).shadowBanned).to.equal(true);//user 2 banned

    // translator bans user 1 successfully
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(true);//user 1 banned
    expect((await User.findOne({ _id: 2 })).shadowBanned).to.equal(true);//user 2 banned
  });

  it('admin bans user - sources permanently blocked', async () => {

    const deleteUserWithId = async (id) => {
      const user = await User.findOne({ _id: id });
      await userLib.deleteAccount(user);
    }
    //create mock user
    res = await initApp('mockuser_2_testmail2_0002', { deviceId: 'deviceId1' });
    res = await BannedSource.find();
    expect(res.length).to.eql(0);//no sources banned

    //ban user
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: 2 });
    expect(user.shadowBanned).to.eql(true);

    res = await BannedSource.find();
    expect(res.length).to.eql(3);//3 banned sources added

    expect(res.some(({ user, sourceType, sourceVal }) => (user === '2' && sourceType === 'deviceId' && sourceVal === 'deviceId1'))
      && res.some(({ user, sourceType, sourceVal }) => (user === '2' && sourceType === 'email' && sourceVal === getMockEmail('testmail2')))
      && res.some(({ user, sourceType, sourceVal }) => (user === '2' && sourceType === 'phoneNumber' && sourceVal === getMockNumber('0002')))
    ).to.eql(true);

    //delete the user
    await deleteUserWithId(2);

    //create new user with same deviceId
    res = await initApp('3', { deviceId: 'deviceId1' });
    expect((await User.findOne({ _id: 3 })).shadowBanned).to.equal(true);//blocked because of permanently banned deviceId
    expect((await User.findOne({ _id: 3 })).bannedReason).to.equal(`Auto-ban: loginSource already banned`);
    expect((await User.findOne({ _id: 3 })).bannedNotes).to.equal(`deviceId: ${user.deviceId}, user: ${user._id}`);
    res = await request(app)
    .get('/v1/admin/user')
    .set('authorization', 0)
    .query({ id: '3' });
    expect(res.status).to.equal(200);
    expect(res.body.bannedSources).to.eql([{sourceType:'deviceId',sourceVal:'deviceId1'}]);

    //create new user with same email
    res = await initApp('mockuser_4_testmail2_0003');
    await waitMs(10);
    expect((await User.findOne({ _id: 4 })).shadowBanned).to.equal(true);//blocked because of permanently banned email
    expect((await User.findOne({ _id: 4 })).bannedReason).to.equal('Auto-ban: loginSource already banned');
    expect((await User.findOne({ _id: 4 })).bannedNotes).to.equal(getMockEmail('testmail2'));
    res = await request(app)
    .get('/v1/admin/user')
    .set('authorization', 0)
    .query({ id: '4' });
    expect(res.status).to.equal(200);
    expect(res.body.bannedSources).to.eql([{ sourceType: 'email', sourceVal: getMockEmail('testmail2') }]);

    //create new user with same number
    res = await initApp('mockuser_5_testmail3_0002');
    await waitMs(10);
    expect((await User.findOne({ _id: 5 })).shadowBanned).to.equal(true);//blocked because of permanently banned phoneNumber
    expect((await User.findOne({ _id: 5 })).bannedReason).to.equal('Auto-ban: loginSource already banned');
    expect((await User.findOne({ _id: 5 })).bannedNotes).to.equal(getMockNumber('0002'));
    res = await request(app)
    .get('/v1/admin/user')
    .set('authorization', 0)
    .query({ id: '5' });
    expect(res.status).to.equal(200);
    expect(res.body.bannedSources).to.eql([{sourceType:'phoneNumber',sourceVal:getMockNumber('0002')}]);

    res = await BannedSource.find();
    expect(res.length).to.eql(3);//3 banned sources added

    console.log(JSON.stringify(res));

    // unban
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '3',
      });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 3 })).shadowBanned).to.equal(false);
    await waitMs(100);

    //banned source removed
    res = await BannedSource.find();
    expect(res.length).to.eql(2);
    expect(
      res.some(({ sourceType, sourceVal }) => (sourceType === 'email' && sourceVal === getMockEmail('testmail2')))
      && res.some(({ sourceType, sourceVal }) => (sourceType === 'phoneNumber' && sourceVal === getMockNumber('0002')))
    ).to.eql(true);

    res = await request(app)
        .get('/v1/admin/user')
        .set('authorization', 0)
        .query({ id: 3 });
      expect(res.status).to.equal(200);
      expect(res.body.bannedSources).to.eql([]);//banned sources not found

    // unban
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '4',
      });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 4 })).shadowBanned).to.equal(false);

    res = await request(app)
        .get('/v1/admin/user')
        .set('authorization', 0)
        .query({ id: 4 });
      expect(res.status).to.equal(200);
      expect(res.body.bannedSources).to.eql([]);//banned sources not found

    //banned source removed
    res = await BannedSource.find();
    expect(res.length).to.eql(0); // all banned source of user 2 is removed when user 3 is unbanned

    // unban
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '5',
      });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 5 })).shadowBanned).to.equal(false);

    res = await request(app)
        .get('/v1/admin/user')
        .set('authorization', 0)
        .query({ id: 5 });
      expect(res.status).to.equal(200);
      expect(res.body.bannedSources).to.eql([]);//banned sources not found

    res = await BannedSource.find();
    expect(res.length).to.eql(0);//banned Source Removed

    //delete users
    for(let i=3;i<=5;i++){
    await deleteUserWithId(i);
    }

    //create new user with same sources don't get blocked
    res = await initApp('3', { deviceId: 'deviceId1' });
    expect((await User.findOne({ _id: 3 })).shadowBanned).to.equal(false);

    res = await initApp('mockuser_4_testmail2_0003');
    await waitMs(10);
    expect((await User.findOne({ _id: 4 })).shadowBanned).to.equal(false);

    res = await initApp('mockuser_5_testmail3_0002');
    await waitMs(10);
    expect((await User.findOne({ _id: 5 })).shadowBanned).to.equal(false);

  });

  it('admin unbans user - all users from same deviceId is unbanned', async () => {
    // create mock user
    let res = await initApp('mockuser_2_testmail2_0002', { deviceId: 'deviceId1' });
    res = await BannedSource.find();
    expect(res.length).to.eql(0); // no sources banned

    // ban user
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    let u = await User.findOne({ _id: 2 });
    expect(u.shadowBanned).to.eql(true);

    res = await BannedSource.find();
    expect(res.length).to.eql(3); // 3 banned sources added

    expect(res.some(({ user, sourceType, sourceVal }) => (user === '2' && sourceType === 'deviceId' && sourceVal === 'deviceId1'))
      && res.some(({ user, sourceType, sourceVal }) => (user === '2' && sourceType === 'email' && sourceVal === getMockEmail('testmail2')))
      && res.some(({ user, sourceType, sourceVal }) => (user === '2' && sourceType === 'phoneNumber' && sourceVal === getMockNumber('0002')))
    ).to.eql(true);

    // create new user with same deviceId
    res = await initApp('3', { deviceId: 'deviceId1' });
    u = await User.findOne({ _id: 3 });
    expect(u.shadowBanned).to.equal(true); // blocked because of banned deviceId
    expect(u.bannedReason).to.equal(`Auto-ban: loginSource already banned`);
    expect(u.bannedNotes).to.equal(`deviceId: ${u.deviceId}, user: 2`);

    // create few new user with same email
    res = await initApp('4', { deviceId: 'deviceId1' });
    await waitMs(10);
    expect((await User.findOne({ _id: 4 })).shadowBanned).to.equal(true); // blocked because of deviceId

    res = await initApp('mockuser_5_testmail2_0002', { deviceId: 'deviceId1' });
    await waitMs(10);

    u = await User.findOne({ _id: 5 });
    expect(u.shadowBanned).to.equal(true);

    // unban user 5 and ban with a different reason
    u.shadowBanned = false;
    u.bannedReason = undefined;
    await u.save();

    setMockPromptResponse('{"ban": true, "reason": "scam"}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '5',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    u = await User.findOne({ _id: 5 });
    expect(u.shadowBanned).to.equal(true);

    // unban user 2 by support
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({ user: '2', notes: 'not scammer' });
    expect(res.status).to.equal(200);
    await waitMs(100);

    // all users should with same deviceId should be unbanned
    let users = await User.find({ _id: { $in: ['2', '3', '4', '5'] } });
    expect(users.length).to.equal(4);
    expect(users.every((_user) => _user.shadowBanned === false)).to.equal(true);

    // All banned sources should be removed
    res = await BannedSource.find();
    expect(res.length).to.eql(0);
  });

  it('admin bans question', async () => {
    // create question
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    kpopId = res.body.interests[0]._id;

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestId: kpopId,
        title: '1',
        text: '1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // no reported questions yet
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // dismiss
    res = await request(app)
      .put('/v1/admin/dismissQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // ban
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    res = await request(app)
      .get('/v1/question/feed')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  it('ban question - language-specific permissions', async () => {
    user = await User.findOne({ _id: 0 });
    user.adminPermissions = { translator: 'de' };
    res = await user.save();

    // report de and fr questions
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '1',
        text: '1',
        language: 'de',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '2',
        text: '2',
        language: 'fr',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q2Id });
    expect(res.status).to.equal(200);

    // admin fetches
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // cannot ban fr
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q2Id,
      });
    expect(res.status).to.equal(403);

    // can ban de
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);
  });

  it('coin award for reported posts', async () => {
    reset();

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    // get initial coins
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const { coins } = res.body.user;

    // create question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 1)
      .send({
        interestName: 'kpop',
        title: '1',
        text: '1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 0)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    const socket0 = await initSocket(0);
    const socketPromise = getSocketPromise(socket0, 'coin reward');

    // ban
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    // reporter should get the coin award
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins + 50);

    const reward = {
      caption: 'Successful User Report',
      rewardAmount: 50,
      newTotal: coins + 50,
    };
    res = await socketPromise;
    expect(res).to.eql(reward);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Thank You');
    expect(notifs.numSent).to.equal(1);
    reset();

    // different user reports again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 2)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // duplicate ban
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    // should not get duplicate award
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins + 50);

    await destroySocket(socket0);
  });

  it('duplicate reports/bans', async () => {
    const initialKarma = 100;
    const user = await User.findOne({ _id: 0 });
    user.karma = initialKarma;
    await user.save();

    // create question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '1',
        text: '1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // should appear on reported questions
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // ban
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    // should not deduct karma
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);

    // different user reports again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 2)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // should not appear on reported questions
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // duplicate ban
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    // should not deduct karma again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);

    // manually set flagged
    const question = await Question.findOne({ _id: q1Id });
    question.flagged = true;
    await question.save();

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(1);

    // ban
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
      });
    expect(res.status).to.equal(200);

    // should remove it from reported queue
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // should not deduct karma again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.karma).to.equal(initialKarma);
  });

  it('test adding single ban reason for question and comment', async () => {
    // create question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '1',
        text: '1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // should appear on reported questions
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // ban with reason
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        bannedReasons: ['Inappropriate Content'],
      });
    expect(res.status).to.equal(200);

    const question = await Question.findOne({ _id: q1Id });
    expect(question.bannedReasons).to.eql(['Inappropriate Content']);

    // create comment
    newQuestion = await createQuestion({
      createdAt: new Date(),
      text: 'Question 1',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '0');
    const q2Id = res.body.questions[0]._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '0')
      .send({
        questionId: q2Id,
        text: 'Comment 1',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;

    // report the comment
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // ban the comment with reason
    res = await request(app)
      .put('/v1/admin/banComment')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
        bannedReasons: ['Inappropriate comment'],
      });
    expect(res.status).to.equal(200);

    const comment = await Comment.findOne({ _id: c1Id });
    expect(comment.bannedReasons).to.eql(['Inappropriate comment']);
  });

  it('test adding multiple ban reasons for question and comment', async () => {
    // create question
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '1',
        text: '1',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    // should appear on reported questions
    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // ban with reason
    res = await request(app)
      .put('/v1/admin/banQuestion')
      .set('authorization', 0)
      .send({
        questionId: q1Id,
        bannedReasons: ['Spam', 'Scammer'],
      });
    expect(res.status).to.equal(200);

    const question = await Question.findOne({ _id: q1Id });
    expect(question.bannedReason).to.equal();
    expect(question.bannedReasons).to.eql(['Spam', 'Scammer']);

    // create comment
    newQuestion = await createQuestion({
      createdAt: new Date(),
      text: 'Question 1',
      interestName: 'questions',
    });
    await newQuestion.save();

    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '0');
    const q2Id = res.body.questions[0]._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '0')
      .send({
        questionId: q2Id,
        text: 'Comment 1',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;

    // report the comment
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // ban the comment with reason
    res = await request(app)
      .put('/v1/admin/banComment')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
        bannedReasons: ['scamming', 'creating multiple accounts'],
      });
    expect(res.status).to.equal(200);

    const comment = await Comment.findOne({ _id: c1Id });
    expect(comment.bannedReason).to.equal();
    expect(comment.bannedReasons).to.eql(['scamming', 'creating multiple accounts']);
  });

  it('admin bans comment', async () => {
    // create comment
    newQuestion = await createQuestion({
      createdAt: new Date(),
      text: 'Question 1',
      interestName: 'questions',
    });
    await newQuestion.save();
    res = await request(app)
      .get('/v1/question/allQuestions')
      .set('authorization', '0');
    const q1Id = res.body.questions[0]._id;
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '0')
      .send({
        questionId: q1Id,
        text: 'Comment 1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;

    // no reported comments yet
    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // report the comment
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // dismiss the comment
    res = await request(app)
      .put('/v1/admin/dismissComment')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(1);

    // report the comment
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // ban the comment
    res = await request(app)
      .put('/v1/admin/banComment')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    res = await request(app)
      .get('/v1/comment')
      .query({ parentId: q1Id })
      .set('authorization', '1');
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);
  });

  it('ban comment - language-specific permissions', async () => {
    user = await User.findOne({ _id: 0 });
    user.adminPermissions = { translator: 'de' };
    res = await user.save();

    // report de and fr
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '1',
        text: '1',
        language: 'de',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '0')
      .send({
        questionId: q1Id,
        text: 'Comment 1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;

    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: '2',
        text: '2',
        language: 'fr',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '0')
      .send({
        questionId: q2Id,
        text: 'Comment 2',
        parentId: q2Id,
      });
    expect(res.status).to.equal(200);
    const c2Id = res.body._id;

    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);

    // admin fetches
    res = await request(app)
      .get('/v1/admin/reportedComments')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.comments.length).to.equal(0);

    // cannot ban fr
    res = await request(app)
      .put('/v1/admin/banComment')
      .set('authorization', 0)
      .send({
        commentId: c2Id,
      });
    expect(res.status).to.equal(403);

    // can ban de
    res = await request(app)
      .put('/v1/admin/banComment')
      .set('authorization', 0)
      .send({
        commentId: c1Id,
      });
    expect(res.status).to.equal(200);
  });

  it('admin views user profile', async () => {
    // non-admin forbidden
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 1)
      .query({
        id: '0',
      });
    expect(res.status).to.equal(403);
    expect(res.body.user).to.equal();
    expect(res.body.metadata).to.equal();

    // admin allowed
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.user._id).to.equal('1');
    expect(res.body.metadata._id).to.equal('1');

    // search by handle
    user = await User.findById('1');
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: user.handle,
      });
    expect(res.status).to.equal(200);
    expect(res.body.user._id).to.equal('1');
    expect(res.body.metadata._id).to.equal('1');

    // not found
    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: 'i',
      });
    expect(res.status).to.equal(404);
  });

  it('many reported questions from banned user', async () => {
    // create reported questions from banned user
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    for (let i = 0; i < pageSize; i++) {
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 1)
        .send({
          interestName: 'kpop',
          title: i.toString(),
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .patch('/v1/question/report')
        .set('authorization', 0)
        .send({ questionId: res.body._id });
      expect(res.status).to.equal(200);
    }

    const user = await User.findOne({ _id: 1 });
    user.shadowBanned = true;
    res = await user.save();

    // post new question from good user
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'kpop',
        title: 'need to ban',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);

    // report it
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 0)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reportedQuestions')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.questions.length).to.equal(0);
  });

  /*
  it('admin gets reports, messages, dismisses reports', async () => {
    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // non-admin forbidden
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 1);
    expect(res.status).to.equal(403);
    expect(res.body.reports).to.equal();

    res = await request(app)
      .get('/v1/admin/messages')
      .set('authorization', 1)
      .query({ reportedBy: '0', reportedUser: '1' });
    expect(res.status).to.equal(403);
    expect(res.body).to.eql({});

    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 1)
      .send({ user: '1' });
    expect(res.status).to.equal(403);

    // admin views reports
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('0');
    expect(res.body.reports[0].reportedUser).to.equal('1');

    // view messages - none found
    res = await request(app)
      .get('/v1/admin/messages')
      .set('authorization', 0)
      .query({ reportedBy: '0', reportedUser: '1' });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql([]);

    res = await request(app)
      .get('/v1/admin/messagesSentByUser')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql([]);

    // set up chat with message
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // admin views messages
    res = await request(app)
      .get('/v1/admin/messages')
      .set('authorization', 0)
      .query({ reportedBy: '0', reportedUser: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].sender).to.equal('1');

    res = await request(app)
      .get('/v1/admin/messagesSentByUser')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].sender).to.equal('1');

    // admin dismisses reports
    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    // no more reports
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    // user is not banned
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(false);

    // view reports for user
    res = await request(app)
      .get('/v1/admin/reportsForUser')
      .set('authorization', 0)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/reportsForUser')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
  });

  it('messages for deleted chats', async () => {
    // set up chat with message
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // unmatch
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // admin views reports
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.reports);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('0');
    expect(res.body.reports[0].reportedUser).to.equal('1');

    // admin views messages
    res = await request(app)
      .get('/v1/admin/messages')
      .set('authorization', 0)
      .query({ reportedBy: '0', reportedUser: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].sender).to.equal('1');
  });

  it('chat deleted before report', async () => {
    // set up chat with message
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);

    // unmatch
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);

    // admin can still view messages sent by the user
    res = await request(app)
      .get('/v1/admin/messagesSentByUser')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].sender).to.equal('1');

    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // admin views reports
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.reports);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('0');
    expect(res.body.reports[0].reportedUser).to.equal('1');

    // admin views messages
    res = await request(app)
      .get('/v1/admin/messages')
      .set('authorization', 0)
      .query({ reportedBy: '0', reportedUser: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].sender).to.equal('1');
  });
  */

  it('view most recent messages sent by user', async () => {
    // set up chat with message
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg2',
      });
    expect(res.status).to.equal(200);

    // view most recent messages in order
    res = await request(app)
      .get('/v1/admin/messagesSentByUser')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    expect(res.body[0].text).to.equal('msg2');
    expect(res.body[1].text).to.equal('msg1');
  });

  it('view deleted chats and messages', async () => {
    // set up chat with messages
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 1)
      .send({
        user: '0',
        text: 'msg1',
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'msg2',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);

    // no deleted chats yet
    res = await request(app)
      .get('/v1/admin/deletedChats')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.deletedChats.length).to.equal(0);

    // unmatch to delete chat
    res = await request(app)
      .patch('/v1/user/unmatch')
      .set('authorization', 1)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    res = await request(app)
      .get('/v1/chat/messaged')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(0);

    // view deleted chat and messages
    res = await request(app)
      .get('/v1/admin/deletedChats')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    console.log(res.body.deletedChats);
    expect(res.body.deletedChats.length).to.equal(1);
    expect(res.body.deletedChats[0].user._id).to.equal('0');
    expect(res.body.deletedChats[0].user.firstName).to.equal('');
    expect(res.body.deletedChats[0].user.pictures).to.eql([]);

    res = await request(app)
      .get('/v1/admin/deletedChatMessages')
      .set('authorization', 0)
      .query({ chatId: res.body.deletedChats[0]._id });
    expect(res.status).to.equal(200);
    console.log(res.body.messages);
    expect(res.body.messages.length).to.equal(2);
    expect(res.body.messages[0].text).to.equal('msg2');
    expect(res.body.messages[0].sender).to.equal('0');
    expect(res.body.messages[1].text).to.equal('msg1');
    expect(res.body.messages[1].sender).to.equal('1');

    // should not error even if user 1 is deleted
    await User.deleteOne({ _id: '1' });

    res = await request(app)
      .get('/v1/admin/deletedChats')
      .set('authorization', 0)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    console.log(res.body.deletedChats);
    expect(res.body.deletedChats.length).to.equal(0);
  });

  /*
  it('multiple reports from one user', async () => {
    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['1'],
        comment: '1',
      });
    expect(res.status).to.equal(200);

    // send another report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    // admin views reports
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.reports);
    expect(res.body.reports.length).to.equal(2);
    expect(res.body.reports[0].reportedBy).to.equal('0');
    expect(res.body.reports[0].reportedUser).to.equal('1');
    expect(res.body.reports[0].reason).to.eql(['1']);
    expect(res.body.reports[0].comment).to.equal('1');
    expect(res.body.reports[1].reportedBy).to.equal('0');
    expect(res.body.reports[1].reportedUser).to.equal('1');
    expect(res.body.reports[1].reason).to.eql(['2']);
    expect(res.body.reports[1].comment).to.equal('2');
  });

  it('auto-dismiss reports for deleted accounts', async () => {
    // user 0 reports user 1
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['1'],
        comment: '1',
      });
    expect(res.status).to.equal(200);

    // user 1 reports user 0
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    // admin views reports
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    console.log(res.body.reports);
    expect(res.body.reports.length).to.equal(2);

    // user 1 deletes account
    const user = await User.findOne({ _id: 1 });
    await userLib.deleteAccount(user);


    // report for user 1 should be auto-dismissed
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('1');
    expect(res.body.reports[0].reportedUser).to.equal('0');
    expect(res.body.reports[0].reason).to.eql(['2']);
    expect(res.body.reports[0].comment).to.equal('2');
  });
  */

  it('china timezone country mismatch', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 10)
      .send({ timezone: 'Asia/Shanghai' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 10)
      .send({
        latitude: 40.7,
        longitude: -74,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    console.log(res.body.reports[0]);
    expect(res.body.reports[0].reason[0]).to.equal('Auto-report: china timezone country mismatch');
    expect(res.body.reports[0].comment).to.equal('actualCountry: United States, signupCountry: China, timezone: Asia/Shanghai');
  });

  it('ignore reports from banned accounts', async () => {
    // user 1 is banned
    const user = await User.findOne({ _id: 1 });
    user.shadowBanned = true;
    res = await user.save();

    // user 1 reports user 0
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    // report is not saved
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);
  });

  /*
  it('auto-dismiss reports from banned accounts', async () => {
    // user 1 reports user 0
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    // user 2 reports user 0
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 2)
      .send({
        user: '0',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0').lean();
    expect(user.metrics.numTotalReports).to.equal(2);
    expect(user.metrics.numPendingReports).to.equal(2);

    // user 1 is banned
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // report from user 1 should be dismissed
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('2');

    user = await User.findById('0').lean();
    expect(user.metrics.numTotalReports).to.equal(2);
    expect(user.metrics.numPendingReports).to.equal(1);

    // user 2 is banned
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
      });
    expect(res.status).to.equal(200);

    // report from user 2 should be dismissed
    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    user = await User.findById('0').lean();
    expect(user.metrics.numTotalReports).to.equal(2);
    expect(user.metrics.numPendingReports).to.equal(0);
  });

  it('report metrics', async () => {
    // report user 1
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1').lean();
    expect(user.metrics.numTotalReports).to.equal(1);
    expect(user.metrics.numPendingReports).to.equal(1);

    // dismiss user 1
    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('1').lean();
    expect(user.metrics.numTotalReports).to.equal(1);
    expect(user.metrics.numPendingReports).to.equal(0);
  });
  */

  it('banned user tries to verify profile', async () => {
    user = await User.findById('1');
    user.shadowBanned = true;
    await user.save();

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
  });
  /* Pose verification disabled
  it('female web user doing pose verification should be auto reported', async () => {
    // male user should not be auto reported
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2)
      .send({ os: 'web' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 2)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 2)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 2)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    // non-web user should not be auto reported
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ os: 'android' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 3)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    // female web user should be auto reported
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ os: 'web' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal(null);
    expect(res.body.reports[0].reportedUser).to.equal('1');
    expect(res.body.reports[0].reason).to.eql(['Auto-report due to web pose verification']);
  });

  it('female user who did pose verification should be auto reported after switching to web', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ os: 'android' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 3)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // switch to web
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ os: 'web' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal(null);
    expect(res.body.reports[0].reportedUser).to.equal('3');
    expect(res.body.reports[0].reason).to.eql(['Auto-report due to web pose verification']);
  });

  it('female user who ever used web and did pose verification should be auto reported', async () => {
    // start web, switch to android
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ os: 'web' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ os: 'android' })
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 3)
      .send({ gender: 'female' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal(null);
    expect(res.body.reports[0].reportedUser).to.equal('3');
    expect(res.body.reports[0].reason).to.eql(['Auto-report due to web pose verification']);
  });
  */

  /*
  it('user banned after trying to verify profile', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
  });
  */

  /*
  it('verify profile sort order', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 2)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 2)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(2);
    expect(res.body.users[0]._id).to.equal('1');
    expect(res.body.users[1]._id).to.equal('2');

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(2);
    expect(res.body.users[0]._id).to.equal('2');
    expect(res.body.users[1]._id).to.equal('1');
  });
  */

  it('verify profile - auto-reject', async () => {
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('This version of the app is no longer supported. Please update to the latest version of Boo. Thank you!');
  });

  it('verify profile - basic', async () => {
    // set config and version
    user = await User.findById('1');
    user.config.allow_pose_verification = true;
    user.appVersion = '1.13.30';
    await user.save();

    reset();
    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    // set personality to make user searchable
    res = await request(app)
      .put('/v1/user/personality')
      .set('authorization', 1)
      .send({
        mbti: 'ISTP',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.reward).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(false);
    let { coins } = res.body;

    // non-admin forbidden
    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 1);
    expect(res.status).to.equal(403);
    res = await request(app)
      .get('/v1/admin/recentlyVerifiedProfiles')
      .set('authorization', 1);
    expect(res.status).to.equal(403);
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 1);
    expect(res.status).to.equal(403);

    // no verification requests yet
    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    res = await request(app)
      .get('/v1/admin/recentlyVerifiedProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    // invalid upload
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1);
    expect(res.status).to.equal(422);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');
    const handle1 = res.body.user.handle;

    // upload profile picture does not change verification status
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    pictureId = res.body.pictures[0];

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');

    // edit profile picture does not change verification status
    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 1)
      .query({ id: pictureId })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    pictureId = res.body[0];

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');

    // valid upload
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');

    // get verification request
    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    console.log(res.body.verification);
    expect(res.body.verification.pictures.length).to.equal(1);
    assert(res.body.verification.pictures[0].includes('1/verification/'));
    expect(res.body.verification.status).to.equal('pending');
    expect(res.body.verification.verifiedBy).to.equal();

    socket = await initSocket(1);
    socketPromise = getSocketPromise(socket, 'verification');

    // reject
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: false,
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Verification Unsuccessful');
    expect(notifs.recent.notification.body).to.equal('Please follow the profile verification guidelines.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'rejected',
        verified: false,
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql({
      verificationStatus: 'rejected',
      verified: false,
    });
    await destroySocket(socket);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(false);
    expect(res.body.coins).to.equal(coins);

    // can upload again after rejection
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');

    // get verification request
    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    console.log(res.body.verification);
    expect(res.body.verification.pictures.length).to.equal(2);
    assert(res.body.verification.pictures[0].includes('1/verification/'));
    assert(res.body.verification.pictures[1].includes('1/verification/'));
    expect(res.body.verification.status).to.equal('pending');
    expect(res.body.verification.verifiedBy).to.equal('0');

    socket = await initSocket(1);
    socketPromise = getSocketPromise(socket, 'verification');

    // approve
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: true,
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Verification Successful');
    expect(notifs.recent.notification.body).to.equal('Your profile has been verified.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'verified',
        verified: true,
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql({
      verificationStatus: 'verified',
      verified: true,
    });
    await destroySocket(socket);

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    coins += 200;

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(true);
    expect(res.body.coins).to.equal(coins);

    // add profile picture changes verification status to pending
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    // get verification request
    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    console.log(res.body.verification);
    expect(res.body.verification.pictures.length).to.equal(2);
    assert(res.body.verification.pictures[0].includes('1/verification/'));
    assert(res.body.verification.pictures[1].includes('1/verification/'));

    // approve again - no duplicate coin reward
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: true,
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Verification Successful');
    expect(notifs.recent.notification.body).to.equal('Your profile has been verified.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'verified',
        verified: true,
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/pending')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);
    res = await request(app)
      .get('/v1/admin/verifyProfile/reverifying')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users).to.eql([]);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(true);
    expect(res.body.coins).to.equal(coins);

    // edit profile picture changes verification status
    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 1)
      .query({ id: pictureId })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    pictureId = res.body[0];

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    // upgrading to 1.11.6 shows status as reverifying
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.11.6' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: true,
      });
    expect(res.status).to.equal(200);

    // delete profile picture changes verification status
    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 1)
      .query({ id: pictureId });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('reverifying');

    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: true,
      });
    expect(res.status).to.equal(200);

    // get recently verified profiles and change to reject
    res = await request(app)
      .get('/v1/admin/recentlyVerifiedProfiles')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');

    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    res = await request(app)
      .get('/v1/user/boo')
      .set('authorization', 0)
      .query({ handle: handle1 });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');

    res = await request(app)
      .get('/v1/coins')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.rewards.verifyProfile.received).to.equal(true);
  });

  it('verify profile - karma must be rounded', async () => {
    // set config and karma to a double
    user = await User.findById('1');
    user.config.allow_pose_verification = true;
    user.karma = 9316.55369225877;
    await user.save();

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // karma should be rounded to an int
    res = await request(app)
      .get('/v1/admin/verifyProfile')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    expect(res.body.users[0].karma).to.equal(9317);
  });

  it('upload pose verification without a profile picture - automatically rejected', async () => {
    // set config
    user = await User.findById('1');
    user.config.allow_pose_verification = true;
    await user.save();

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({ fcmToken: 'token1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 25));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');
  });

  /* Pose verification disabled, latest change does not have 10 attempts limit
  it('limit 10 verification attempts per 24 hours', async () => {
    setReplicateMockResponse({ output: ['Dismiss', 'Incorrect', 'Pose'] });
    fakeOpenaiClient.chat = {
      completions: {
        async create(params) {
          console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
          return {
            usage: {
              prompt_tokens: 10,
              completion_tokens: 10,
            },
            choices: [{
              message: {
                content: 'Dismiss Incorrect pose'
              },
            }],
          }
        }
      }
    }

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    for (let i = 1; i <= 10; i++) {
      res = await request(app)
        .post('/v1/user/profileVerificationPicture')
        .set('authorization', 0)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      await new Promise((r) => setTimeout(r, 50));

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.13.0' });
      expect(res.status).to.equal(200);
      expect(res.body.user.verified).to.equal(false);
      expect(res.body.user.verificationStatus).to.equal('rejected');
      expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

      attempts = await PoseVerification.find();
      expect(attempts.length).to.equal(i);
    }

    // next attempt is ignored, status remains pending
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');

    attempts = await PoseVerification.find();
    expect(attempts.length).to.equal(10);

    // a different user can still attempt
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    attempts = await PoseVerification.find().sort('-_id');
    expect(attempts.length).to.equal(11);
    expect(attempts[0].user).to.equal('1');

    // simulate 24 hours
    await PoseVerification.updateMany({}, { $set: { createdAt: moment().subtract(24, 'hours').toDate() } });
    await User.updateOne({_id: '0'}, { $set: { 'verification.updatedAt': Date.now() } });

    // pending status now turns to rejected
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');

    // user can try again
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 0)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

    attempts = await PoseVerification.find().sort('-_id');
    expect(attempts.length).to.equal(12);
    expect(attempts[0].user).to.equal('0');
  });
  */

  it('upload pose verification without a face match - automatically rejected', async () => {
    // set config
    user = await User.findById('1');
    user.config.allow_pose_verification = true;
    await user.save();

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({ fcmToken: 'token1' });
    expect(res.status).to.equal(200);

    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ UnmatchedFaces: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    socket = await initSocket(1);
    socketPromise = getSocketPromise(socket, 'verification');

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 50));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('Verification Unsuccessful');
    expect(notifs.recent.notification.body).to.equal('Reason: Make sure your first profile picture is a picture of you, and only you. Please follow the profile verification guidelines.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'rejected',
        verified: false,
        rejectionReason: 'Make sure your first profile picture is a picture of you, and only you.',
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql({
      verificationStatus: 'rejected',
      verified: false,
      rejectionReason: 'Make sure your first profile picture is a picture of you, and only you.',
    });

    await destroySocket(socket);
  });

  /* Pose verification disabled, manual review added
  it('automated pose comparison - automatically rejected', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({ fcmToken: 'token1' });
    expect(res.status).to.equal(200);

    setReplicateMockResponse({ output: ['Dismiss', 'Incorrect', 'Pose'] });

    fakeOpenaiClient.chat = {
      completions: {
        async create(params) {
          console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
          return {
            usage: {
              prompt_tokens: 10,
              completion_tokens: 10,
            },
            choices: [{
              message: {
                content: 'Dismiss Incorrect pose'
              },
            }],
          }
        }
      }
    }

    socket = await initSocket(1);
    socketPromise = getSocketPromise(socket, 'verification');

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Incorrect pose');

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('Verification Unsuccessful');
    expect(notifs.recent.notification.body).to.equal('Reason: Incorrect pose. Please follow the profile verification guidelines.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'rejected',
        verified: false,
        rejectionReason: 'Incorrect pose',
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql({
      verificationStatus: 'rejected',
      verified: false,
      rejectionReason: 'Incorrect pose',
    });

    await destroySocket(socket);
  });
  */

  it('1.13.30 rejection notification sent on exitFlashSale event', async () => {
    // set config
    user = await User.findById('1');
    user.config.allow_pose_verification = true;
    await user.save();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.30' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({ fcmToken: 'token1' });
    expect(res.status).to.equal(200);

    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ UnmatchedFaces: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    socket = await initSocket(1);
    socketPromise = getSocketPromise(socket, 'verification');

    res = await request(app)
      .patch('/v1/user/events')
      .set('authorization', 1)
      .send({ exitFlashSale: true });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('Verification Unsuccessful');
    expect(notifs.recent.notification.body).to.equal('Reason: Make sure your first profile picture is a picture of you, and only you. Please follow the profile verification guidelines.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'rejected',
        verified: false,
        rejectionReason: 'Make sure your first profile picture is a picture of you, and only you.',
      }),
    });
    reset();

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql({
      verificationStatus: 'rejected',
      verified: false,
      rejectionReason: 'Make sure your first profile picture is a picture of you, and only you.',
    });

    await destroySocket(socket);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');
  });

  /* Pose verification disabled, manual review added
  it('automated pose comparison - automatically approved', async () => {
    setReplicateMockResponse({ output: ['Verify'] });
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 1)
      .send({ fcmToken: 'token1' });
    expect(res.status).to.equal(200);

    fakeOpenaiClient.chat = {
      completions: {
        async create(params) {
          console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
          return {
            usage: {
              prompt_tokens: 10,
              completion_tokens: 10,
            },
            choices: [{
              message: {
                content: 'Verify'
              },
            }],
          }
        }
      }
    }

    socket = await initSocket(1);
    socketPromise = getSocketPromise(socket, 'verification');

    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 50));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('Verification Successful');
    expect(notifs.recent.notification.body).to.equal('Your profile has been verified.');
    expect(notifs.recent.data).to.eql({
      verification: JSON.stringify({
        verificationStatus: 'verified',
        verified: true,
      }),
    });
    expect(notifs.numSent).to.equal(1);
    reset();

    res = await socketPromise.catch((err) => { console.error(err); });
    expect(res).to.eql({
      verificationStatus: 'verified',
      verified: true,
    });

    await destroySocket(socket);
  });
  */

  /*
  it('config off - rejected then pending', async () => {
    // rejected because no profile picture
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 10));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Not same person');

    // add profile picture
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // config off, so should be pending now
    res = await request(app)
      .post('/v1/user/profileVerificationPicture')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 10));

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('pending');
    expect(res.body.user.rejectionReason).to.equal();
  });
  */

  it('verify profile queued', async () => {
    sinon.stub(constants, 'getVerifyProfilePageSize').returns(1);
    const clock = sinon.useFakeTimers();

    // users 1 - 3 submit verification picture
    for (let i = 1; i <= 3; i++) {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      user = await User.findById(i.toString());
      await user.setVerificationStatus('pending');
      await user.save();
    }

    // report user 1
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['2'],
        comment: '2',
      });
    expect(res.status).to.equal(200);

    // get user 1 and user 2 from the queue
    res = await request(app)
      .get('/v1/admin/verifyProfile/pendingQueued')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    expect(res.body.users[0].reports.length).to.equal(1);
    expect(res.body.users[0].reports[0].comment).to.equal('2');

    res = await request(app)
      .get('/v1/admin/verifyProfile/pendingQueued')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('2');
    expect(res.body.users[0].reports.length).to.equal(0);

    // reject user 2
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '2',
        verified: false,
        rejectionReason: 'Incorrect pose',
      });
    expect(res.status).to.equal(200);

    // 10 minutes pass
    clock.tick(20 * 3600 * 1000);

    // get user 1 and user 3 from the queue
    res = await request(app)
      .get('/v1/admin/verifyProfile/pendingQueued')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('1');
    expect(res.body.users[0].reports.length).to.equal(1);
    expect(res.body.users[0].reports[0].comment).to.equal('2');

    res = await request(app)
      .get('/v1/admin/verifyProfile/pendingQueued')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(1);
    expect(res.body.users[0]._id).to.equal('3');
    expect(res.body.users[0].reports.length).to.equal(0);

    // no one left in queue
    res = await request(app)
      .get('/v1/admin/verifyProfile/pendingQueued')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.users.length).to.equal(0);

    clock.restore();
  });

  it('verify profile without image', async () => {
    res = await request(app)
      .patch('/v1/admin/verifyProfile')
      .set('authorization', 0)
      .send({
        user: '1',
        verified: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });

  it('update user location', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal(null);

    // invalid country
    res = await request(app)
      .put('/v1/admin/userLocation')
      .set('authorization', 0)
      .send({
        user: '1',
        city: 'Honolulu',
        state: 'Hawaii',
        country: 'Unite State',
      });
    expect(res.status).to.equal(422);

    // state abbreviation
    res = await request(app)
      .put('/v1/admin/userLocation')
      .set('authorization', 0)
      .send({
        user: '1',
        city: 'Austin',
        state: 'TX',
        country: 'United States',
      });
    expect(res.status).to.equal(200);
    expect(res.body.location).to.equal('Austin, TX 🇺🇸');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal('Austin, TX 🇺🇸');
    expect(res.body.user.actualLocation).to.equal('Austin, TX 🇺🇸');

    // country abbreviation
    res = await request(app)
      .put('/v1/admin/userLocation')
      .set('authorization', 0)
      .send({
        user: '1',
        city: 'New York City',
        state: 'New York',
        country: 'US',
      });
    expect(res.status).to.equal(200);
    expect(res.body.location).to.equal('New York City, NY 🇺🇸');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal('New York City, NY 🇺🇸');

    // no abbreviations
    res = await request(app)
      .put('/v1/admin/userLocation')
      .set('authorization', 0)
      .send({
        user: '1',
        city: 'Honolulu',
        state: 'Hawaii',
        country: 'United States',
      });
    expect(res.status).to.equal(200);
    expect(res.body.location).to.equal('Honolulu, HI 🇺🇸');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal('Honolulu, HI 🇺🇸');

    // location update disabled - should not change to UK
    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.5,
        longitude: 0,
      });
    expect(res.status).to.equal(200);
    expect(res.body).to.eql({
      location: 'Honolulu, HI 🇺🇸',
      locationComponents: {
        city: 'Honolulu',
        state: 'Hawaii',
        country: 'United States',
      },
      actualLocationComponents: {
        city: 'Honolulu',
        state: 'Hawaii',
        country: 'United States',
      },
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal('Honolulu, HI 🇺🇸');

    // delete location - location should change to UK on next location update
    res = await request(app)
      .delete('/v1/admin/userLocation')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.location).to.equal('Honolulu, HI 🇺🇸');

    res = await request(app)
      .put('/v1/user/location')
      .set('authorization', 1)
      .send({
        latitude: 51.5,
        longitude: 0,
      });
    expect(res.status).to.equal(200);
    expect(res.body.location).to.equal('Blackwall, England 🇬🇧');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.location).to.equal('Blackwall, England 🇬🇧');
  });

  it('update user birthday', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.birthday).to.equal(null);

    res = await request(app)
      .put('/v1/admin/userBirthday')
      .set('authorization', 0)
      .send({
        user: '2',
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(404);

    res = await request(app)
      .put('/v1/admin/userBirthday')
      .set('authorization', 0)
      .send({
        user: '1',
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.birthday).to.equal('1990-01-01T00:00:00.000Z');
  });

  it('update user premium expiration', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.premium).to.equal(false);

    res = await request(app)
      .put('/v1/admin/userPremiumExpiration')
      .set('authorization', 0)
      .send({
        user: '1',
        year: 2100,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.premium).to.equal(true);

    res = await request(app)
      .put('/v1/admin/userPremiumExpiration')
      .set('authorization', 0)
      .send({
        user: '1',
        year: 1990,
        month: 1,
        day: 1,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.premium).to.equal(false);
  });

  it('get recently dismissed reports', async () => {
    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/recentlyDismissedReports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('0');
    expect(res.body.reports[0].reportedUser).to.equal('1');
    expect(res.body.reports[0].status).to.equal('dismissed');
    expect(res.body.reports[0].handledBy).to.equal();

    res = await request(app)
      .get('/v1/admin/recentlyVerifiedReports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);
  });

  it('coin award for successful reports', async () => {
    setMockPromptResponse('{"ban": true, "reason": "scam"}');

    reset();

    res = await request(app)
      .put('/v1/user/fcmToken')
      .set('authorization', 0)
      .send({ fcmToken: 'token0' });
    expect(res.status).to.equal(200);

    // get initial coins
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const { coins } = res.body.user;

    const socket0 = await initSocket(0);
    const socketPromise = getSocketPromise(socket0, 'coin reward');

    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // reporter should not get the coin award yet
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins);

    doc = await Report.findOne();
    expect(doc.grantCoinAwardAt).to.not.equal();
    expect(doc.coinAwardGrantedAt).to.equal();

    // trigger worker job, coin award not granted yet
    res = await request(app)
      .post('/v1/worker/grantCoinAwardsForReports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins);

    doc = await Report.findOne();
    expect(doc.grantCoinAwardAt).to.not.equal();
    expect(doc.coinAwardGrantedAt).to.equal();

    // modify grantCoinAwardAt
    doc.grantCoinAwardAt = Date.now();
    await doc.save();

    // trigger worker job, coin award granted
    res = await request(app)
      .post('/v1/worker/grantCoinAwardsForReports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    doc = await Report.findOne();
    expect(doc.grantCoinAwardAt).to.equal();
    expect(doc.coinAwardGrantedAt).to.not.equal();

    // reporter should get the coin award
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins + 50);

    const reward = {
      caption: 'Successful User Report',
      rewardAmount: 50,
      newTotal: coins + 50,
    };
    res = await socketPromise;
    expect(res).to.eql(reward);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('Thank You');
    expect(notifs.numSent).to.equal(1);
    reset();

    // duplicate verify
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // should not get duplicate award
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins + 50);

    await destroySocket(socket0);
  });

  it('no coin award for dismissed reports', async () => {
    // get initial coins
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    const { coins } = res.body.user;

    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    // dismiss the report
    res = await request(app)
      .put('/v1/admin/dismissReports')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // no coin award
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins);

    // now ban
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    // no coin award since report was previously dismissed
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins);
  });

  /*
  it('get recently verified reports', async () => {
    // send report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/admin/recentlyDismissedReports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(0);

    res = await request(app)
      .get('/v1/admin/recentlyVerifiedReports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedBy).to.equal('0');
    expect(res.body.reports[0].reportedUser).to.equal('1');
    expect(res.body.reports[0].status).to.equal('verified');
    expect(res.body.reports[0].handledBy).to.equal('0');
  });
  */

  it('give user coins', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    coins = res.body.user.coins;

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.metadata.coins).to.equal(coins);

    res = await request(app)
      .patch('/v1/admin/incrementCoins')
      .set('authorization', 0)
      .send({
        user: '1',
        coins: 100,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(coins + 100);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.metadata.coins).to.equal(coins + 100);
  });

  it('give user neurons', async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .send({appVersion:'1.13.17'})
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    neurons = res.body.user.numBooAINeurons;

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.metadata.numBooAINeurons).to.equal(neurons);

    res = await request(app)
      .patch('/v1/admin/incrementNeurons')
      .set('authorization', 0)
      .send({
        user: '1',
        numBooAINeurons: 100,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.numBooAINeurons).to.equal(neurons + 100);

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({
        id: '1',
      });
    expect(res.status).to.equal(200);
    expect(res.body.metadata.numBooAINeurons).to.equal(neurons + 100);
  });

  describe('interests', () => {
    beforeEach(async () => {
      reset();
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', 0)
        .send({ fcmToken: 'token0' });
      expect(res.status).to.equal(200);

      await Interest.deleteMany();

      user = await User.findOne({ _id: 0 });
      user.adminPermissions = { approveInterest: true };
      res = await user.save();

      sinon.stub(interestLib, 'shouldInterestBeApproved')
        .callsFake((params) => {
          const impl = function (resolve, reject) {
            resolve(true);
          };
          return new Promise(impl);
        });
    });

    it('interest name validation', async () => {
      // name too long
      res = await request(app)
        .post('/v1/interest')
        .set('authorization', 0)
        .send({ name: 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa' });
      expect(res.status).to.equal(422);

      // name gets cleaned
      res = await request(app)
        .post('/v1/interest')
        .set('authorization', 0)
        .send({ name: 'A+b c#d-1,2,3' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/interest/popular')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.interests.length).to.equal(1);
      expect(res.body.interests[0].name).to.equal('abcd123');
    });

    it('approve interests', async () => {
      // create the interest
      res = await request(app)
        .post('/v1/interest')
        .set('authorization', 0)
        .send({ name: 'new' });
      expect(res.status).to.equal(200);
      expect(res.body.approved).to.equal(true);

      // interest found, not pending
      res = await request(app)
        .get('/v1/interest')
        .set('authorization', 0)
        .query({ name: 'new' });
      expect(res.status).to.equal(200);
      expect(res.body.interest.name).to.equal('new');
      expect(res.body.interest.numFollowers).to.equal(1);

      res = await request(app)
        .get('/v1/interest/popular')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.interests.length).to.equal(1);

      res = await request(app)
        .get('/v1/admin/pendingInterests')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.interests.length).to.equal(0);

      // notification received
      resetTime = Date.now();
      await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
      expect(notifs.recent.token).to.equal('token0');
      expect(notifs.recent.notification.title).to.equal('');
      expect(notifs.recent.notification.body).to.equal('Your interest #new has been approved!');
      expect(notifs.numSent).to.equal(1);

      // interest should be auto-added to profile
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.user.interestNames).to.eql(['new']);

      // can add the interest now
      res = await request(app)
        .put('/v1/user/interests')
        .set('authorization', 0)
        .send({
          interestNames: ['new'],
        });
      expect(res.status).to.equal(200);

      // can post to the interest
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 0)
        .send({
          interestName: 'new',
          title: 'title1',
          text: 'text1',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .post('/v1/question/image')
        .set('authorization', 0)
        .query({ questionId: res.body._id })
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      // cannot resubmit interest
      res = await request(app)
        .post('/v1/interest')
        .set('authorization', 0)
        .send({ name: 'new' });
      expect(res.status).to.equal(422);
    });

    it('reject interests', async () => {
      interestLib.shouldInterestBeApproved.restore();
      sinon.stub(interestLib, 'shouldInterestBeApproved')
        .callsFake((params) => {
          const impl = function (resolve, reject) {
            resolve(false);
          };
          return new Promise(impl);
        });

      // create the interest
      res = await request(app)
        .post('/v1/interest')
        .set('authorization', 0)
        .send({ name: 'new' });
      expect(res.status).to.equal(200);
      expect(res.body.approved).to.equal(false);

      // interest not found, not pending
      res = await request(app)
        .get('/v1/interest')
        .set('authorization', 0)
        .query({ name: 'new' });
      expect(res.status).to.equal(404);

      res = await request(app)
        .get('/v1/interest/popular')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.interests.length).to.equal(0);

      res = await request(app)
        .get('/v1/admin/pendingInterests')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.interests.length).to.equal(0);

      // notification received
      resetTime = Date.now();
      await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
      expect(notifs.recent.token).to.equal('token0');
      expect(notifs.recent.notification.title).to.equal('');
      expect(notifs.recent.notification.body).to.equal('Your interest #new was not approved.');
      expect(notifs.numSent).to.equal(1);

      // cannot resubmit interest
      res = await request(app)
        .post('/v1/interest')
        .set('authorization', 0)
        .send({ name: 'new' });
      expect(res.status).to.equal(422);
    });
  });

  it('updates user phone number', async () => {
    // throws error because phone number is not string
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: [1] });
    console.log(res);
    expect(res.status).to.equal(422);

    // throws error because invalid phone number
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: '12345' });
    console.log(res);
    expect(res.status).to.equal(422);

    // throws error because user not yet in database
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '2', phoneNumber: getMockNumber('0002') });
    console.log(res);
    expect(res.status).to.equal(404);

    // throws error because user not yet in firebase
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: getMockNumber('0001') });
    console.log(res);
    expect(res.status).to.equal(409);

    /// /create User 1 in firebase
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 'mockuser_1_testmail1_0001');
    expect(res.status).to.equal(200);

    // succeeds changing number
    let startTime = Date.now();
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: getMockNumber('0002') });
    expect(res.status).to.equal(200);

    // user modified
    res = await User.findOne({ _id: '1' }, { _id: 1, phoneNumber: 1 });
    expect(res.phoneNumber).to.eql(getMockNumber('0002'));

    // update log created
    res = await PhoneUpdateLogs.findOne({ updatedAt: { $gt: startTime } });
    expect(res).to.include({
      adminId: '0',
      userId: '1',
      old_num: getMockNumber('0001'),
      new_num: getMockNumber('0002'),
    });

    // succeeds removing number
    startTime = Date.now();
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: null });
    expect(res.status).to.equal(200);

    // user modified
    res = await User.findOne({ _id: '1' }, { _id: 1, phoneNumber: 1 });
    expect(res.phoneNumber).to.eql(null);

    // log created
    res = await PhoneUpdateLogs.findOne({ updatedAt: { $gt: startTime } });
    expect(res).to.include({
      adminId: '0',
      userId: '1',
      old_num: getMockNumber('0002'),
      new_num: null,
    });

    /// /create User 2 in firebase and db
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 'mockuser_2_testmail2_0002');
    expect(res.status).to.equal(200);

    // should result in error if another user already has same number!
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: getMockNumber('0002') });
    expect(res.status).to.equal(409);

    // succeeds if updating number to a unique one!
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: getMockNumber('0003') });
    expect(res.status).to.equal(200);

    // succeeds if updating number to the existing one !
    res = await request(app)
      .put('/v1/admin/userNumber')
      .set('authorization', '0')
      .send({ user: '1', phoneNumber: getMockNumber('0003') });
    expect(res.status).to.equal(200);
  });

  it('updates user email', async () => {
    // throws error because email is not string
    res = await request(app)
      .put('/v1/admin/userEmail')
      .set('authorization', '0')
      .send({ user: '1', email: [1] });
    expect(res.status).to.equal(422);

    // throws error because user not yet in firebase
    res = await request(app)
      .put('/v1/admin/userEmail')
      .set('authorization', '0')
      .send({ user: '1', email: getMockEmail('testmail2') });
    expect(res.status).to.equal(409);

    /// /create User 1 in firebase
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 'mockuser_1_testmail1_0001');
    expect(res.status).to.equal(200);

    // throws error because email is invalid
    res = await request(app)
      .put('/v1/admin/userEmail')
      .set('authorization', '0')
      .send({ user: '1', email: 'invalid' });
    expect(res.status).to.equal(409);

    // succeeds changing
    let startTime = Date.now();
    res = await request(app)
      .put('/v1/admin/userEmail')
      .set('authorization', '0')
      .send({ user: '1', email: getMockEmail('testmail2') });
    expect(res.status).to.equal(200);

    // user modified
    res = await User.findOne({ _id: '1' }, { _id: 1, email: 1 });
    expect(res.email).to.eql(getMockEmail('testmail2'));

    // update log created
    res = await PhoneUpdateLogs.findOne({ updatedAt: { $gt: startTime } });
    expect(res).to.include({
      adminId: '0',
      userId: '1',
      old_email: getMockEmail('testmail1'),
      new_email: getMockEmail('testmail2'),
    });

    /// /create User 3 in firebase and db
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 'mockuser_3_testmail3_0003');
    expect(res.status).to.equal(200);

    // should result in error if another user already has same email!
    res = await request(app)
      .put('/v1/admin/userEmail')
      .set('authorization', '0')
      .send({ user: '1', email: getMockEmail('testmail3') });
    expect(res.status).to.equal(409);
  });

  it('delete user admin', async () => {
    // set "support" and "manager" permissions
    user = await User.findOne({ _id: 0 });
    user.adminPermissions = { manager: true, support: true };
    res = await user.save();

    // create user 2
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // total 3 users now
    let users = await User.find();
    assert(users.length === 3);

    // try deleting user 2
    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 2 });

    // user not found with valid deletionRequestDate
    expect(res.status).to.equal(404);

    // set valid deletionRequestDate for user 1 and 2
    await User.updateMany({ _id: { $in: [1, 2] } }, { deletionRequestDate: new Date(2000, 1, 1) });

    // try deleting user 2
    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 2 });

    // user 2 deleted successfully
    expect(res.status).to.equal(200);
    users = await User.find();
    assert(users.length === 2 && users[0]._id === '0' && users[1]._id === '1');
  });

  it('top admins can force delete accounts', async () => {
    // add "all" permissions
    const user = await User.findOne({ _id: 0 });
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .delete('/v1/admin/user')
      .set('authorization', 0)
      .send({ userId: 1 });
    expect(res.status).to.equal(200);

    // deleted successfully
    expect(res.status).to.equal(200);
    users = await User.find();
    assert(users.length === 1 && users[0]._id === '0');
  });

  describe('tempban user', () => {
    let notificationStub;
    beforeEach(async () => {
      notificationStub = sinon.stub(fakeAdminMessaging, 'send').callsFake(async (params) => {
        console.log('fake notification called', JSON.stringify(params));
        return { response: 'success' };
      });
    });
    afterEach(async () => {
      notificationStub.restore();
    });

    it('temporary ban user', async () => {
      /// /check User 1
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal(undefined);
      expect(res.body.tempBanEndAt).to.equal(undefined);

      // invalid Input
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: ['text'], banUser: 1 });
      expect(res.status).to.equal(422);

      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: null });
      expect(res.status).to.equal(422);

      // set fcm token to receive notifications
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', 1)
        .send({
          fcmToken: '0',
        });
      expect(res.status).to.equal(200);

      // ok request
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1 });
      expect(res.status).to.equal(200);

      // wait for notifications
      await waitMs(10);
      // notification sent/prof
      assert(notificationStub.calledOnce);

      // should return values for ban reason and end date
      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal('text');
      expect(res.body.tempBanEndAt).to.not.equal(undefined);
      expect(res.body.tempBanBy).to.equal(undefined);

      user = await User.findById('1');
      expect(user.tempBanBy).to.equal('0');
      expect(user.banHistory.length).to.equal(1);
      expect(user.banHistory[0].action).to.equal('tempBan');
      expect(user.banHistory[0].by).to.equal('0');
      expect(user.banHistory[0].reason).to.equal('text');

      // to initialise with current date
      const clock = sinon.useFakeTimers(Date.now());
      // total 20 hours passed
      clock.tick(20 * 3600 * 1000);

      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal('text');
      expect(res.body.tempBanEndAt).to.not.equal(undefined);

      // 24 hours pased
      clock.tick(4 * 3600 * 1000);

      // ok request
      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
        .send({ reason: 'text' });
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal(undefined);
      expect(res.body.tempBanEndAt).to.equal(undefined);

      clock.restore();
    });

    it('undo temp ban', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1 });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal('text');
      expect(res.body.tempBanEndAt).to.not.equal(undefined);

      res = await request(app)
        .put('/v1/admin/undoTempBan')
        .set('authorization', 0)
        .send({ user: 1, notes: 'okay' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal(undefined);
      expect(res.body.tempBanEndAt).to.equal(undefined);

      user = await User.findById('1');
      expect(user.banHistory.length).to.equal(2);
      expect(user.banHistory[1].action).to.equal('undoTempBan');
      expect(user.banHistory[1].by).to.equal('0');
      expect(user.banHistory[1].notes).to.equal('okay');
    });

    it('with infringing chat', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);

      // set up chat
      res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: '1',
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .patch('/v1/user/approve')
        .set('authorization', 1)
        .send({
          user: '0',
        });
      expect(res.status).to.equal(200);

      // send some messages from both users
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: 'msg1',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: '1',
          text: 'msg2',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/message/image')
        .set('authorization', 1)
        .query({ recipient: 0 })
        .attach('image', validImagePath)
      expect(res.status).to.equal(200);

      // invalid
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingChatWithUser: [3] });
      expect(res.status).to.equal(422);

      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingChatWithUser: '3' });
      expect(res.status).to.equal(422);

      // valid
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingChatWithUser: '0' });
      expect(res.status).to.equal(200);

      // should return values for ban reason and end date
      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal('text');
      expect(res.body.tempBanEndAt).to.not.equal(undefined);
      expect(res.body.tempBanBy).to.equal(undefined);

      user = await User.findById('1');
      expect(user.tempBanBy).to.equal('0');
      console.log(JSON.stringify(user.banHistory,null,2));
      expect(user.banHistory.length).to.equal(1);
      expect(user.banHistory[0].action).to.equal('tempBan');
      expect(user.banHistory[0].by).to.equal('0');
      expect(user.banHistory[0].reason).to.equal('text');
      expect(user.banHistory[0].evidence.messages.length).to.equal(2);
      expect(user.banHistory[0].evidence.messages[0].image).to.include('1/evidence/');
      expect(user.banHistory[0].evidence.messages[1].text).to.equal('msg1');
    });

    it('with infringing post', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);

      // set up posts
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 1)
        .send({
          interestName: 'chess',
          title: 'first post',
          mediaUploadPending: true,
        });
      expect(res.status).to.equal(200);
      q1Id = res.body._id;

      let images = JSON.stringify([
        { type: 'new', fileIndex: 0, altText: 'Inserted new image1' },
        { type: 'new', fileIndex: 1, altText: 'Inserted new image2' },
        { type: 'new', fileIndex: 2, altText: 'Inserted new image3' },
      ]);
      res = await request(app)
        .post('/v1/question/images')
        .set('authorization', 1)
        .query({ questionId: q1Id })
        .attach('images', validImagePath)
        .attach('images', validImagePath)
        .attach('images', validImagePath)
        .field('imagesArray', images);
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/question')
        .set('authorization', 0)
        .send({
          interestName: 'chess',
          title: 'first post',
        });
      expect(res.status).to.equal(200);
      q2Id = res.body._id;

      // invalid
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingPost: 'test' });
      expect(res.status).to.equal(422);

      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingPost: q2Id });
      expect(res.status).to.equal(422);

      // valid
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingPost: q1Id });
      expect(res.status).to.equal(200);

      // should return values for ban reason and end date
      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal('text');
      expect(res.body.tempBanEndAt).to.not.equal(undefined);
      expect(res.body.tempBanBy).to.equal(undefined);

      user = await User.findById('1');
      expect(user.tempBanBy).to.equal('0');
      console.log(JSON.stringify(user.banHistory,null,2));
      expect(user.banHistory.length).to.equal(1);
      expect(user.banHistory[0].action).to.equal('tempBan');
      expect(user.banHistory[0].by).to.equal('0');
      expect(user.banHistory[0].reason).to.equal('text');
      expect(user.banHistory[0].evidence.post.title).to.equal('first post');
      expect(user.banHistory[0].evidence.post.images.length).to.equal(3);
      expect(user.banHistory[0].evidence.post.images[0].image).to.include('1/evidence/');
      expect(user.banHistory[0].evidence.post.images[1].image).to.include('1/evidence/');
      expect(user.banHistory[0].evidence.post.images[2].image).to.include('1/evidence/');
    });

    it('with infringing comment', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);

      // set up comments
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 1)
        .send({
          interestName: 'chess',
          title: 'first post',
          mediaUploadPending: true,
        });
      expect(res.status).to.equal(200);
      q1Id = res.body._id;

      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 1)
        .send({
          questionId: q1Id,
          parentId: q1Id,
        });
      expect(res.status).to.equal(200);
      const c0Id = res.body._id;

      res = await request(app)
        .post('/v1/comment/image')
        .set('authorization', 1)
        .query({ commentId: c0Id })
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/comment')
        .set('authorization', 0)
        .send({
          questionId: q1Id,
          parentId: q1Id,
        });
      expect(res.status).to.equal(200);
      const c1Id = res.body._id;

      // invalid
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingComment: 'test' });
      expect(res.status).to.equal(422);

      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingComment: c1Id });
      expect(res.status).to.equal(422);

      // valid
      res = await request(app)
        .put('/v1/admin/tempBan')
        .set('authorization', 0)
        .send({ reason: 'text', banUser: 1, infringingComment: c0Id });
      expect(res.status).to.equal(200);

      // should return values for ban reason and end date
      res = await request(app)
        .get('/v1/user/')
        .set('authorization', 1)
        .send({ appVersion: '1.11.45' });
      expect(res.status).to.equal(200);
      expect(res.body.tempBanReason).to.equal('text');
      expect(res.body.tempBanEndAt).to.not.equal(undefined);
      expect(res.body.tempBanBy).to.equal(undefined);

      user = await User.findById('1');
      expect(user.tempBanBy).to.equal('0');
      console.log(JSON.stringify(user.banHistory,null,2));
      expect(user.banHistory.length).to.equal(1);
      expect(user.banHistory[0].action).to.equal('tempBan');
      expect(user.banHistory[0].by).to.equal('0');
      expect(user.banHistory[0].reason).to.equal('text');
      expect(user.banHistory[0].evidence.comment.text).to.equal('');
      expect(user.banHistory[0].evidence.comment.image).to.include('1/evidence/');
    });
  });

  describe('question candidates', async () => {
    let notificationStub;
    let lastSocketEventData;
    let userSocket;
    let totalChatMessages = 0;

    const submitNotification = {
      title: 'Boo',
      body: 'Thank you for sending your question! You will be notified if selected.',
    };

    const selectedNotification = (text, date) => {
      date = DateTime.fromJSDate(date, { zone: 'utc' });
      return {
        title: 'Boo',
        body: `Your question "${text}", has been scheduled for ${date.monthLong} ${date.day}, ${date.year}`,
      };
    };

    const supportMessageSubmit = {
      sender: BOO_SUPPORT_ID,
      text: 'Thank you for sending your question! You will be notified if selected.',
    };
    const supportMessageSelected = (text, date) => {
      date = DateTime.fromJSDate(date, { zone: 'utc' });
      return {
        sender: BOO_SUPPORT_ID,
        text: `Your question "${text}", has been scheduled for ${date.monthLong} ${date.day}, ${date.year}`,
      };
    };
    const getDateAfterDays = (days) => DateTime.fromJSDate(new Date()).plus({ days }).toJSDate();
    beforeEach(async () => {
      notificationStub = sinon.stub(fakeAdminMessaging, 'send').callsFake(async (params) => {
        console.log('fake notification called', JSON.stringify(params));
        return { response: 'success' };
      });
      userSocket = await initSocket(1);
      userSocket.on('approved chat', (data) => {
        lastSocketEventData = { type: 'new', data };
        totalChatMessages++;
        console.log(`UserSocket newChat:${JSON.stringify(data, null, 1)}`);
      });
      userSocket.on('message', (data) => {
        lastSocketEventData = { type: 'message', data };
        totalChatMessages++;
        console.log(`UserSocket chatMessage:${JSON.stringify(data, null, 1)}`);
      });
      await createSupportUser();

      user = await User.findOne({ _id: 0 });
      user.adminPermissions = { approveQod: true };
      res = await user.save();
    });
    afterEach(async () => {
      notificationStub.restore();
      await destroySocket(userSocket);
    });

    it('question candidates', async () => {
      const ids = [];
      let totalMessageExpected = 0;

      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', 1)
        .send({ fcmToken: 'token0' });
      expect(res.status).to.equal(200);

      const pushLatestCandidateId = async () => {
        const latestCandidate = await QuestionCandidate.findOne({}, { _id: 1 }).sort({ _id: -1 });
        ids.push(latestCandidate._id.valueOf());
      };

      // store initial user coins
      res = await request(app)
        .get('/v1/coins')
        .set('authorization', '1');
      expect(res.status).to.equal(200);

      let userCoins = res.body.coins;

      // user 1 submits qod1
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'boo1',
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);
      await pushLatestCandidateId();

      // new chat created
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('new');
      expect(lastSocketEventData.data.lastMessage).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // user 1 submits qod2
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'boo2',
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);
      await pushLatestCandidateId();

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // User 1 submits qod3 with isAnonymous true
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'boo3',
          isAnonymous: true,
        });
      expect(res.status).to.eql(200);
      await pushLatestCandidateId();

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // check coins
      res = await request(app)
        .get('/v1/coins')
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      expect(res.body.coins).to.equal(userCoins);// coins not awarded for submitting

      const getQ = (id, text, user, isAnonymous, status, reviewedAt, reviewedBy) => ({
        id,
        text,
        createdBy: user,
        isAnonymous,
        status,
      });

      // admin fetches candidates

      // invalid input
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ count: 'a' });
      expect(res.status).to.eql(422);
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ lastSent: 123 });
      expect(res.status).to.eql(422);
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ order: 'abcd' });
      expect(res.status).to.eql(422);
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'abcd' });
      expect(res.status).to.eql(422);

      // get candidates(default by most recent sorting)
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({});
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(2);// only two candidates are fetched because max count = 2 unless set by env
      expect(res.body.candidates[0]).to.include(getQ(ids[2], 'boo3', '1', true, 'pending'));
      expect(res.body.candidates[1]).to.include(getQ(ids[1], 'boo2', '1', false, 'pending'));

      // pagination admin fetched candidates
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({
          lastSent: res.body.candidates[1].id,
        });
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0]).to.include(getQ(ids[0], 'boo1', '1', false, 'pending'));

      // sorting admin fetched candidates
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({
          order: 'asc',
        });
      expect(res.body.candidates.length).to.equal(2);
      expect(res.body.candidates[0]).to.include(getQ(ids[0], 'boo1', '1', false, 'pending'));
      expect(res.body.candidates[1]).to.include(getQ(ids[1], 'boo2', '1', false, 'pending'));

      // limit count admin fetched candidates
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({
          count: 1,
        });
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0]).to.include(getQ(ids[2], 'boo3', '1', true, 'pending'));

      const timeNow = new Date();

      // reject qod 1
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: ids[0],
          status: 'rejected',
        });
      expect(res.body).to.eql({});

      res = await request(app)
        .get('/v1/coins')
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      expect(res.body.coins).to.equal(userCoins);// coins not awarded on rejection

      expect(totalChatMessages).to.eql(totalMessageExpected);// no new chat message recieved
      assert(notificationStub.notCalled);// no notification sent

      // admin fetched candidates(only pending candidates show)
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({});
      expect(res.body.candidates.length).to.equal(2);
      expect(res.body.candidates[0]).to.include(getQ(ids[2], 'boo3', '1', true, 'pending'));
      expect(res.body.candidates[1]).to.include(getQ(ids[1], 'boo2', '1', false, 'pending'));

      // filter by status admin fetched candidates
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'rejected' });
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0]).to.include(getQ(ids[0], 'boo1', '1', false, 'rejected'));
      expect(res.body.candidates[0].reviewedBy).to.equal('0');
      let reviewDate = new Date(res.body.candidates[0].reviewedAt);
      expect(reviewDate).to.be.greaterThan(timeNow);
      expect(reviewDate).to.be.lessThan(new Date());

      // user fetches posts
      res = await request(app)
        .get('/v1/question/feed')
        .query({})
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .query({})
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      // approve qod 2
      const approveTime2 = new Date();
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: ids[1],
          status: 'approved',
        });
      expect(res.body).to.eql({});

      userCoins += 50;
      // check user coins
      res = await request(app)
        .get('/v1/coins')
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      // coins awarded on approval
      expect(res.body.coins).to.equal(userCoins);

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSelected('boo2', new Date()));

      // notification received
      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(selectedNotification('boo2', new Date()));
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // user on old version does not see createdBy
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy).to.equal(null);

      // update version
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.11.47' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.47' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/coins')
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      userCoins = res.body.coins;

      // user fetches posts
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy._id).to.equal('1');

      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy._id).to.equal('1');

      // web route should also see createdBy
      res = await request(app)
        .get('/web/question/feed');
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].profilePreview._id).to.equal('1');

      const approveTime3 = new Date();
      // approve qod 3
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: ids[2],
          status: 'approved',
        });
      expect(res.body).to.eql({});

      userCoins += 50;
      res = await request(app)
        .get('/v1/coins')
        .set('authorization', '1');
      expect(res.status).to.equal(200);
      expect(res.body.coins).to.equal(userCoins);// coins awarded on approval

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSelected('boo3', getDateAfterDays(1)));

      // notification received
      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(selectedNotification('boo3', getDateAfterDays(1)));
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // filter by status  admin fetched candidates
      // invalid input error
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'appr' });

      expect(res.status).to.equal(422);

      // question 2 shows up first in ascending order because question 1 is filtered out
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'approved', order: 'asc' });
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(2);

      expect(res.body.candidates[0]).to.include(
        getQ(ids[1], 'boo2', '1', false, 'approved'),
      );
      reviewDate = new Date(res.body.candidates[0].reviewedAt);
      expect(res.body.candidates[0].reviewedBy).to.equal('0');
      expect(reviewDate).to.be.greaterThan(approveTime2);
      expect(reviewDate).to.be.lessThan(new Date());

      expect(res.body.candidates[1]).to.include(
        getQ(ids[2], 'boo3', '1', true, 'approved'),
      );
      reviewDate = new Date(res.body.candidates[1].reviewedAt);
      expect(res.body.candidates[1].reviewedBy).to.equal('0');
      expect(reviewDate).to.be.greaterThan(approveTime3);
      expect(reviewDate).to.be.lessThan(new Date());

      // user fetches posts
      // new question does not show in feed because new qod is shown from next day
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy._id).to.equal('1');

      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy._id).to.equal('1');

      // to initialise with current date
      let clock = sinon.useFakeTimers(Date.now());
      // total 20 hours passed
      clock.tick(20 * 3600 * 1000);

      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy._id).to.equal('1');

      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo2');
      expect(res.body.questions[0].createdBy._id).to.equal('1');

      clock.tick(5 * 3600 * 1000);

      // user fetches posts
      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(2);
      expect(res.body.questions[1].text).to.equal('boo2');
      expect(res.body.questions[1].createdBy._id).to.equal('1');
      expect(res.body.questions[0].text).to.equal('boo3');
      expect(res.body.questions[0].createdBy).to.equal(null);

      // only latest qod shows up
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo3');
      expect(res.body.questions[0].createdBy).to.equal(null);// createdBy is null because isAnonymous true

      clock.restore();

      // user 1 submits qod4
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'boo4',
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);
      await pushLatestCandidateId();

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // delete user 1's account
      user = await User.findById('1');
      await userLib.deleteAccount(user);

      // admin fetched candidates(only pending candidates show)
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({});
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0]).to.include(getQ(ids[3], 'boo4', null, false, 'pending'));

      // approve candidate submitted by deleted user
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: ids[3],
          status: 'approved',
        });
      expect(res.body).to.eql({});

      clock = sinon.useFakeTimers(Date.now());
      // total 2 days pass
      clock.tick(48 * 3600 * 1000);

      // user fetches posts
      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(3);
      expect(res.body.questions[2].text).to.equal('boo2');
      expect(res.body.questions[2].createdBy).to.equal(null);// createdBy becomes null if user deleted
      expect(res.body.questions[1].text).to.equal('boo3');
      expect(res.body.questions[1].createdBy).to.equal(null);
      expect(res.body.questions[0].text).to.equal('boo4');
      expect(res.body.questions[0].createdBy).to.equal(null);// because user deleted

      // only latest qod shows up
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('boo4');
      expect(res.body.questions[0].createdBy).to.equal(null);// createdBy is null because user deleted already

      clock.restore();
    });

    it('qod with language', async () => {
      // user 1 submits question with language
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang',
          isAnonymous: false,
          language: 'de',
        });
      expect(res.status).to.eql(200);

      // language field when fetch qods
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);
      expect(res.body.candidates[0]).to.include({
        text: 'booLang',
        language: 'de',
      });

      // query by language
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'pending', language: 'en' });
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(0);

      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .query({ status: 'pending', language: 'de' });
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);

      // approve candidate with language
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: res.body.candidates[0].id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);
      expect(res.body).to.eql({});

      // no posts without language query
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      // user fetches posts with language included
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1)
        .query({ language: 'de' });
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('booLang');
      expect(res.body.questions[0].language).to.equal('de');

      res = await request(app)
        .get('/v1/question/allQuestions')
        .set('authorization', 1)
        .query({ language: 'de' });
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('booLang');
      expect(res.body.questions[0].language).to.equal('de');
    });

    it('qod with null/en language', async () => {
      // user 1 submits question without language
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang',
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);

      // language set to 'en'
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);
      expect(res.body.candidates[0]).to.include({
        text: 'booLang',
        language: 'en',
      });

      // approve candidate with language
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: res.body.candidates[0].id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);
      expect(res.body).to.eql({});

      // english post appears
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);

      // user fetches posts with language included
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1)
        .query({ language: 'en' });
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('booLang');
      expect(res.body.questions[0].language).to.equal('en');
    });

    it('qod with blank language', async () => {
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang',
          isAnonymous: false,
          language: '',
        });
      expect(res.status).to.eql(200);

      // language set to 'en'
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);
      expect(res.body.candidates[0]).to.include({
        text: 'booLang',
        language: 'en',
      });
    });

    it('qod with banned user', async () => {

      // update app version
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.11.47' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.47' });
      expect(res.status).to.equal(200);

      // submit and approve qod from user 1
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang',
          isAnonymous: false,
          language: 'en',
        });
      expect(res.status).to.eql(200);

      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      expect(res.status).to.eql(200);

      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: res.body.candidates[0].id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);

      // ban user 1
      u = await User.findById('1');
      u.shadowBanned = true;
      await u.save();

      // user 0 should not see profile in qod
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].createdBy).to.equal(null);

      // user 1 should see profile in qod
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].createdBy._id).to.equal('1');
    });

    it('qod with blocked user', async () => {

      // update app version
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 0)
        .send({ appVersion: '1.11.47' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1)
        .send({ appVersion: '1.11.47' });
      expect(res.status).to.equal(200);

      // submit and approve qod from user 1
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang',
          isAnonymous: false,
          language: 'en',
        });
      expect(res.status).to.eql(200);

      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      expect(res.status).to.eql(200);

      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: res.body.candidates[0].id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);

      // block user 1
      user = await request(app)
        .patch('/v1/user/block')
        .set('authorization', 0)
        .send({
          user: '1',
        });
      expect(res.status).to.equal(200);

      // user 0 should not see profile in qod
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].createdBy).to.equal(null);

      // user 1 should see profile in qod
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].createdBy._id).to.equal('1');
    });

    it('gap between qod', async () => {
      // user 1 submits question
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: '1',
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);

      // approve candidate
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);

      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: res.body.candidates[0].id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);

      // post appears
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('1');

      // change qod date
      const qod = await Question.findOne({});
      qod.createdAt = new Date(2000, 1, 1, 0, 0, 0, 0);
      await qod.save();

      // no qod
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(0);

      // user 1 submits another question
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: '2',
          isAnonymous: false,
        });
      expect(res.status).to.eql(200);

      // approve candidate
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);

      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: res.body.candidates[0].id,
          status: 'approved',
        });
      expect(res.status).to.eql(200);

      // post appears
      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('2');
    });

    it('qod should not count toward post limit', async () => {
      constants.getDailyPostLimit.restore();
      sinon.stub(constants, 'getDailyPostLimit').returns(1);

      // submit and approve several qod
      for (let i = 0; i < 2; i++) {
        res = await request(app)
          .post('/v1/question/submitQod')
          .set('authorization', 1)
          .send({
            text: 'booLang',
            isAnonymous: false,
          });
        expect(res.status).to.eql(200);

        res = await request(app)
          .get('/v1/admin/questionCandidates')
          .set('authorization', 0)
          .send({ status: 'pending' });
        console.log(res.body);
        expect(res.status).to.eql(200);

        res = await request(app)
          .post('/v1/admin/questionCandidates/status')
          .set('authorization', 0)
          .send({
            id: res.body.candidates[0].id,
            status: 'approved',
          });
        expect(res.status).to.eql(200);
      }

      // user should still be able to post
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 1)
        .send({
          interestName: 'kpop',
          title: '0',
          text: '0',
        });
      expect(res.status).to.equal(200);

      // now user should hit limit
      res = await request(app)
        .post('/v1/question')
        .set('authorization', 1)
        .send({
          interestName: 'kpop',
          title: '0',
          text: '0',
        });
      expect(res.status).to.equal(403);
    });

    it('qod translator language check', async () => {
      async function setTranslatorLanguage(language) {
        await User.updateOne({ _id: '0' }, {
          $set: { adminPermissions: { translator: language } },
        });
      }

      // user 1 submits question en language
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang',
          isAnonymous: false,
          language: 'en',
        });
      expect(res.status).to.eql(200);

      // user 1 submits question de language
      res = await request(app)
        .post('/v1/question/submitQod')
        .set('authorization', 1)
        .send({
          text: 'booLang2',
          isAnonymous: false,
          language: 'de',
        });
      expect(res.status).to.eql(200);

      const ids = [];

      // set translator language de
      await setTranslatorLanguage('de');
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);
      expect(res.body.candidates[0]).to.include({
        text: 'booLang2',
        language: 'de',
      });
      ids.push(res.body.candidates[0].id);// 0 id is for de question candidate

      // set translator language en
      await setTranslatorLanguage('en');
      res = await request(app)
        .get('/v1/admin/questionCandidates')
        .set('authorization', 0)
        .send({ status: 'pending' });
      console.log(res.body);
      expect(res.status).to.eql(200);
      expect(res.body.candidates.length).to.eql(1);
      expect(res.body.candidates[0]).to.include({
        text: 'booLang',
        language: 'en',
      });
      ids.push(res.body.candidates[0].id);// 0 id is for de question candidate

      // try approving de candidate
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: ids[0],
          status: 'approved',
        });
      expect(res.status).to.eql(403);
      expect(res.body).to.eql({});

      // success for en
      res = await request(app)
        .post('/v1/admin/questionCandidates/status')
        .set('authorization', 0)
        .send({
          id: ids[1],
          status: 'approved',
        });
      expect(res.status).to.eql(200);
      expect(res.body).to.eql({});

      res = await request(app)
        .get('/v1/question/feed')
        .set('authorization', 1)
        .query({ language: 'en' });
      expect(res.status).to.equal(200);
      expect(res.body.questions.length).to.equal(1);
      expect(res.body.questions[0].text).to.equal('booLang');
      expect(res.body.questions[0].language).to.equal('en');
    });
  });

  it('should not auto-ban more than once for the same reason', async () => {
    // auto-ban
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ timezone: 'Africa/Accra' });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(true);

    // unban
    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 0)
      .send({
        user: '1',
      });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(false);

    // should not be auto-banned again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ timezone: 'Africa/Accra' });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 1 })).shadowBanned).to.equal(false);
  });

  /*
  describe('database profiles', () => {
    let candidateId;

    beforeEach(async () => {
      await Category.create({
        id: 1,
        name: 'test',
        slug: 'test',
      });
      await Subcategory.create({
        id: 1,
        name: 'test',
        slug: 'test',
        category: 1,
      });
      await databaseLib.downloadCategoriesToFiles();
      await databaseLib.loadCategoriesFromDatabase();

      user = await User.findOne({ _id: 0 });
      user.adminPermissions = { approveDatabase: true };
      res = await user.save();

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          mbti: 'ESTP',
          subcategories: [1],
        });
      expect(res.status).to.equal(200);
      candidateId = res.body._id;

      res = await request(app)
        .get('/v1/admin/database/profileCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0]._id).to.equal(candidateId);
      expect(res.body.candidates[0].name).to.equal('name');
      expect(res.body.candidates[0].subcategories[0].name).to.equal('test');
    });

    it('error handling', async () => {
      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          subcategories: '1',
        });
      expect(res.status).to.equal(422);

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          subcategories: [],
        });
      expect(res.status).to.equal(422);

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          subcategories: [2],
        });
      expect(res.status).to.equal(422);

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          subcategories: ['abc'],
        });
      expect(res.status).to.equal(422);

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          subcategories: [1],
          mbti: 'name',
        });
      expect(res.status).to.equal(500);

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          subcategories: [1],
          horoscope: null,
        });
      expect(res.status).to.equal(500);
    });

    it('approve profile candidate', async () => {
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      const initialCoins = res.body.user.coins;
      const initialKarma = res.body.user.karma;

      const {
        dbUploadKarmaReceived,
        dbUploadCoinsReceived,
        numDbUploads,
      } = await User.findOne({ _id: 1 }, { numDbUploads: 1, dbUploadCoinsReceived: 1, dbUploadKarmaReceived: 1 });

      res = await request(app)
        .post('/v1/admin/database/profileCandidate/status')
        .set('authorization', 0)
        .send({
          _id: candidateId,
          status: 'approved',
        });
      expect(res.status).to.equal(200);

      const profiles = await Profile.find();
      expect(profiles.length).to.equal(1);
      expect(profiles[0].id).to.equal(1);
      expect(profiles[0].name).to.equal('name');
      expect(profiles[0].subcategories).to.eql([1]);

      res = await request(app)
        .get('/v1/admin/database/profileCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(0);

      // web route for getting profile should include createdBy handle
      res = await request(app)
        .put('/v1/user/handle')
        .set('authorization', 1)
        .send({ handle: 'handle1' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/web/database/profile')
        .query({ id: 1 });
      expect(res.status).to.equal(200);
      expect(res.body.profile.name).to.equal('name');
      expect(res.body.profile.createdBy).to.eql({ _id: '1', handle: 'handle1' });

      // user should have received coins and karma
      res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', 1);
      expect(res.status).to.equal(200);
      expect(res.body.user.coins).to.equal(initialCoins + 10);
      expect(res.body.user.karma).to.equal(initialKarma + 10);

      // user document updated in db with coins earned and dbUploads
      const updatedUserData = await User.findOne({ _id: 1 }, { numDbUploads: 1, dbUploadCoinsReceived: 1, dbUploadKarmaReceived: 1 });
      expect(updatedUserData.numDbUploads).to.eql(numDbUploads + 1);
      expect(updatedUserData.dbUploadCoinsReceived).to.eql(dbUploadCoinsReceived + 10);
      expect(updatedUserData.dbUploadKarmaReceived).to.eql(dbUploadKarmaReceived + 10);
    });

    it('reject profile candidate', async () => {
      res = await request(app)
        .post('/v1/admin/database/profileCandidate/status')
        .set('authorization', 0)
        .send({
          _id: candidateId,
          status: 'rejected',
        });
      expect(res.status).to.equal(200);

      const profiles = await Profile.find();
      expect(profiles.length).to.equal(0);

      res = await request(app)
        .get('/v1/admin/database/profileCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(0);
    });

    it('add image to profile candidate', async () => {
      res = await request(app)
        .post('/v1/database/profileImageCandidate')
        .set('authorization', 1)
        .query({ _id: candidateId })
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/admin/database/profileCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0].image).to.match(/^database\/profiles\/.+\.jpg/);
      expect(res.body.candidates[0].imageSource).to.equal();

      res = await request(app)
        .get('/v1/admin/database/profileImageCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(0);

      res = await request(app)
        .post('/v1/admin/database/profileCandidate/status')
        .set('authorization', 0)
        .send({
          _id: candidateId,
          status: 'approved',
        });
      expect(res.status).to.equal(200);

      const profiles = await Profile.find();
      expect(profiles.length).to.equal(1);
      expect(profiles[0].image).to.match(/^database\/profiles\/.+\.jpg/);
      expect(profiles[0].imageSource).to.equal();

      res = await request(app)
        .get('/web/database/profile')
        .query({ id: 1 });
      expect(res.status).to.equal(200);
      expect(res.body.profile.image).to.match(/^database\/profiles\/.+\.jpg/);
      expect(res.body.profile.imageSource).to.equal();
    });

    it('add image to profile candidate with image source', async () => {
      res = await request(app)
        .post('/v1/database/profileImageCandidate')
        .set('authorization', 1)
        .query({ _id: candidateId })
        .attach('image', validImagePath)
        .field({ imageSource: 'source' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/admin/database/profileCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0].image).to.match(/^database\/profiles\/.+\.jpg/);
      expect(res.body.candidates[0].imageSource).to.equal('source');

      res = await request(app)
        .get('/v1/admin/database/profileImageCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(0);

      res = await request(app)
        .post('/v1/admin/database/profileCandidate/status')
        .set('authorization', 0)
        .send({
          _id: candidateId,
          status: 'approved',
        });
      expect(res.status).to.equal(200);

      const profiles = await Profile.find();
      expect(profiles.length).to.equal(1);
      expect(profiles[0].image).to.match(/^database\/profiles\/.+\.jpg/);
      expect(profiles[0].imageSource).to.equal('source');

      res = await request(app)
        .get('/web/database/profile')
        .query({ id: 1 });
      expect(res.status).to.equal(200);
      expect(res.body.profile.image).to.match(/^database\/profiles\/.+\.jpg/);
      expect(res.body.profile.imageSource).to.equal('source');
    });

    describe('add image candidate to approved profiles', () => {
      let profileId;
      let imageCandidateId;

      beforeEach(async () => {
        res = await request(app)
          .post('/v1/admin/database/profileCandidate/status')
          .set('authorization', 0)
          .send({
            _id: candidateId,
            status: 'approved',
          });
        expect(res.status).to.equal(200);

        const profiles = await Profile.find();
        expect(profiles[0].image).to.equal();
        profileId = profiles[0]._id.toString();

        res = await request(app)
          .post('/v1/database/profileImageCandidate')
          .set('authorization', 1)
          .query({ _id: profileId })
          .attach('image', validImagePath)
          .field({ imageSource: 'source' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .get('/v1/admin/database/profileCandidate')
          .set('authorization', 0);
        expect(res.status).to.equal(200);
        expect(res.body.candidates.length).to.equal(0);

        res = await request(app)
          .get('/v1/admin/database/profileImageCandidate')
          .set('authorization', 0);
        expect(res.status).to.equal(200);
        expect(res.body.candidates.length).to.equal(1);
        console.log(JSON.stringify(res.body.candidates[0], null, 2));
        expect(res.body.candidates[0].image).to.match(/^database\/profiles\/.+\.jpg/);
        expect(res.body.candidates[0].imageSource).to.equal('source');
        expect(res.body.candidates[0].profile._id).to.equal(profileId);
        expect(res.body.candidates[0].profile.name).to.equal('name');
        expect(res.body.candidates[0].profile.subcategories[0].name).to.equal('test');
        imageCandidateId = res.body.candidates[0]._id;
      });

      it('approve', async () => {
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 1);
        expect(res.status).to.equal(200);
        const initialCoins = res.body.user.coins;
        const initialKarma = res.body.user.karma;

        const {
          numDbUploads,
          dbUploadCoinsReceived,
          dbUploadKarmaReceived,
        } = await User.findOne({ _id: 1 }, { numDbUploads: 1, dbUploadCoinsReceived: 1, dbUploadKarmaReceived: 1 });

        res = await request(app)
          .post('/v1/admin/database/profileImageCandidate/status')
          .set('authorization', 0)
          .send({
            _id: imageCandidateId,
            status: 'approved',
          });
        expect(res.status).to.equal(200);

        const profiles = await Profile.find();
        expect(profiles.length).to.equal(1);
        expect(profiles[0].image).to.match(/^database\/profiles\/.+\.jpg/);
        expect(profiles[0].imageSource).to.equal('source');

        res = await request(app)
          .get('/v1/admin/database/profileImageCandidate')
          .set('authorization', 0);
        expect(res.status).to.equal(200);
        expect(res.body.candidates.length).to.equal(0);

        // web route for getting profile should include createdBy handle
        res = await request(app)
          .put('/v1/user/handle')
          .set('authorization', 1)
          .send({ handle: 'handle1' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .get('/web/database/profile')
          .query({ id: 1 });
        expect(res.status).to.equal(200);
        expect(res.body.profile.name).to.equal('name');
        expect(res.body.profile.createdBy).to.eql({ _id: '1', handle: 'handle1' });

        // user should have received coins and karma
        res = await request(app)
          .put('/v1/user/initApp')
          .set('authorization', 1);
        expect(res.status).to.equal(200);
        expect(res.body.user.coins).to.equal(initialCoins + 10);
        expect(res.body.user.karma).to.equal(initialKarma + 10);

        // user document updated in db with coins earned and dbUploads
        const updatedUserData = await User.findOne({ _id: 1 }, { numDbUploads: 1, dbUploadCoinsReceived: 1, dbUploadKarmaReceived: 1 });
        expect(updatedUserData.numDbUploads).to.eql(numDbUploads + 1);
        expect(updatedUserData.dbUploadCoinsReceived).to.eql(dbUploadCoinsReceived + 10);
        expect(updatedUserData.dbUploadKarmaReceived).to.eql(dbUploadKarmaReceived + 10);
      });

      it('reject', async () => {
        res = await request(app)
          .post('/v1/admin/database/profileImageCandidate/status')
          .set('authorization', 0)
          .send({
            _id: imageCandidateId,
            status: 'rejected',
          });
        expect(res.status).to.equal(200);

        const profiles = await Profile.find();
        expect(profiles.length).to.equal(1);
        expect(profiles[0].image).to.equal();

        res = await request(app)
          .get('/v1/admin/database/profileImageCandidate')
          .set('authorization', 0);
        expect(res.status).to.equal(200);
        expect(res.body.candidates.length).to.equal(0);
      });

      it('overwrite image - createdBy should be updated', async () => {
        res = await request(app)
          .post('/v1/admin/database/profileImageCandidate/status')
          .set('authorization', 0)
          .send({
            _id: imageCandidateId,
            status: 'approved',
          });
        expect(res.status).to.equal(200);

        // now user 0 submits an image and approves it
        res = await request(app)
          .post('/v1/database/profileImageCandidate')
          .set('authorization', 0)
          .query({ _id: profileId })
          .attach('image', validImagePath)
          .field({ imageSource: 'source' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .get('/v1/admin/database/profileImageCandidate')
          .set('authorization', 0);
        expect(res.status).to.equal(200);
        expect(res.body.candidates.length).to.equal(1);
        imageCandidateId = res.body.candidates[0]._id;

        res = await request(app)
          .post('/v1/admin/database/profileImageCandidate/status')
          .set('authorization', 0)
          .send({
            _id: imageCandidateId,
            status: 'approved',
          });
        expect(res.status).to.equal(200);

        // createdBy should be set to user 0
        res = await request(app)
          .put('/v1/user/handle')
          .set('authorization', 0)
          .send({ handle: 'handle0' });
        expect(res.status).to.equal(200);

        res = await request(app)
          .get('/web/database/profile')
          .query({ id: 1 });
        expect(res.status).to.equal(200);
        expect(res.body.profile.name).to.equal('name');
        expect(res.body.profile.createdBy).to.eql({ _id: '0', handle: 'handle0' });
      });
    });
  });

  describe('database profiles with new subcategory', () => {
    let candidateId;

    beforeEach(async () => {
      await Category.create({
        id: 1,
        name: 'Category',
        slug: 'category',
      });

      user = await User.findOne({ _id: 0 });
      user.adminPermissions = { approveDatabase: true };
      res = await user.save();

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          mbti: 'ESTP',
          newSubcategory: {
            name: 'Subcategory',
            categorySlug: 'category',
          },
        });
      expect(res.status).to.equal(200);
      candidateId = res.body._id;

      res = await request(app)
        .get('/v1/admin/database/profileCandidate')
        .set('authorization', 0);
      expect(res.status).to.equal(200);
      expect(res.body.candidates.length).to.equal(1);
      expect(res.body.candidates[0]._id).to.equal(candidateId);
      expect(res.body.candidates[0].name).to.equal('name');
      expect(res.body.candidates[0].newSubcategory.name).to.equal('Subcategory');
      expect(res.body.candidates[0].newSubcategory.categorySlug).to.equal('category');
    });

    it('error handling', async () => {
      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          newSubcategory: '1',
        });
      expect(res.status).to.equal(422);

      res = await request(app)
        .post('/v1/database/profileCandidate')
        .set('authorization', 1)
        .send({
          name: 'name',
          newSubcategory: {
            name: 'Subcategory',
            categorySlug: 'c',
          },
        });
      expect(res.status).to.equal(422);
    });

    it('approve profile candidate', async () => {
      let subcategories = await Subcategory.find();
      expect(subcategories.length).to.equal(0);

      res = await request(app)
        .post('/v1/admin/database/profileCandidate/status')
        .set('authorization', 0)
        .send({
          _id: candidateId,
          status: 'approved',
        });
      expect(res.status).to.equal(200);

      subcategories = await Subcategory.find();
      expect(subcategories.length).to.equal(1);
      expect(subcategories[0].id).to.equal(1);
      expect(subcategories[0].name).to.equal('Subcategory');
      expect(subcategories[0].slug).to.equal('subcategory');
      expect(subcategories[0].category).to.equal(1);

      const profiles = await Profile.find();
      expect(profiles.length).to.equal(1);
      expect(profiles[0].id).to.equal(1);
      expect(profiles[0].name).to.equal('name');
      expect(profiles[0].subcategories).to.eql([1]);
    });

    it('reject profile candidate', async () => {
      res = await request(app)
        .post('/v1/admin/database/profileCandidate/status')
        .set('authorization', 0)
        .send({
          _id: candidateId,
          status: 'rejected',
        });
      expect(res.status).to.equal(200);

      const profiles = await Profile.find();
      expect(profiles.length).to.equal(0);

      const subcategories = await Subcategory.find();
      expect(subcategories.length).to.equal(0);
    });
  });
  */

  describe('user submitted translations', async () => {
    const REWARD_COINS = 25;
    let notificationStub;
    let lastSocketEventData;
    let userSocket;
    let totalChatMessages = 0;

    const textSubmit = 'Thank you for helping us in making Boo better.';
    const textApproved = 'Your translation has been approved.';

    const submitNotification = {
      title: 'Boo',
      body: textSubmit,
    };
    const supportMessageSubmit = {
      sender: BOO_SUPPORT_ID,
      text: textSubmit,
    };

    const selectedNotification = {
      title: 'Boo',
      body: textApproved,
    };

    const supportMessageSelected = {
      sender: BOO_SUPPORT_ID,
      text: textApproved,
    };

    beforeEach(async () => {
      notificationStub = sinon.stub(fakeAdminMessaging, 'send').callsFake(async (params) => {
        console.log('fake notification called tr', JSON.stringify(params));
        return { response: 'success' };
      });
      userSocket = await initSocket(1);
      userSocket.on('approved chat', (data) => {
        lastSocketEventData = { type: 'new', data };
        totalChatMessages++;
        console.log(`UserSocket newChat:${JSON.stringify(data, null, 1)}`);
      });
      userSocket.on('message', (data) => {
        lastSocketEventData = { type: 'message', data };
        totalChatMessages++;
        console.log(`UserSocket chatMessage:${JSON.stringify(data, null, 1)}`);
      });
      await initApp(BOO_SUPPORT_ID);
    });
    afterEach(async () => {
      notificationStub.restore();
      await destroySocket(userSocket);
    });

    it('translations', async () => {
      user = await User.findOne({ _id: 0 });
      user.adminPermissions = { all: true };
      res = await user.save();

      const ids = [];
      let totalMessageExpected = 0;

      await setFcmToken(1, 'token0');// to receive notifications

      const pushLatestId = async () => {
        const lastTranslation = await Translation.findOne({}, { _id: 1 }).sort({ _id: -1 });
        ids.push(lastTranslation._id.valueOf());
      };

      // store initial user coins
      res = await fetchCoinData(1);
      let userCoins = res.coins;

      // user 1 submits translation
      const submitTranslation = (params) => request(app)
        .post('/v1/user/submitTranslation')
        .set('authorization', 1)
        .send(params);

      res = await submitTranslation({
        currentTranslation: 'text0',
        correctTranslation: 'TEXT0',
        language: 'en',
      });
      expect(res.status).to.eql(200);
      await pushLatestId();

      // new chat created
      await waitMs(100);
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('new');
      expect(lastSocketEventData.data.lastMessage).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // user 1 submits another translation
      res = await submitTranslation({
        currentTranslation: 'text1',
        correctTranslation: 'TEXT1',
        language: 'en',
      });
      expect(res.status).to.eql(200);
      await pushLatestId();

      // new message
      await waitMs(100);
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // User 1 submits translation with details key
      res = await submitTranslation({
        currentTranslation: 'text2',
        correctTranslation: 'TEXT2',
        language: 'en',
        details: 'details2',
      });
      expect(res.status).to.eql(200);
      await pushLatestId();

      // new message
      await waitMs(100);
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // User 1 submits translation with language key
      res = await submitTranslation({
        currentTranslation: 'text3',
        correctTranslation: 'TEXT3',
        language: 'de',
      });
      expect(res.status).to.eql(200);
      await pushLatestId();

      // new message
      await waitMs(100);
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSubmit);

      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(submitNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // check coins
      res = await fetchCoinData(1);
      // coins not awarded for submitting
      expect(res.coins).to.equal(userCoins);

      const verifyTranslation = (translation, index, userId, status, language, hasDetails, hasImage) => {
        expect(translation._id).to.equal(ids[index]);
        expect(translation.createdBy).to.equal(userId);
        if (hasImage) {
          assert(translation.refImage.includes(`translations/${ids[index]}/`));
        } else {
          assert(translation.refImage === undefined);
        }
        expect(translation.currentTranslation).to.equal(`text${index}`);
        expect(translation.correctTranslation).to.equal(`TEXT${index}`);
        expect(translation.status).to.equal(status);
        expect(translation.details).to.equal(hasDetails ? `details${index}` : undefined);
        expect(translation.language).to.equal(language);
      };

      // admin fetches translations

      // invalid input
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ count: 'a' });
      expect(res.status).to.eql(422);
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ lastSent: 123 });
      expect(res.status).to.eql(422);
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ order: 'abcd' });
      expect(res.status).to.eql(422);
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ status: 'abcd' });
      expect(res.status).to.eql(422);

      // get translations(default by most recent)
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .send({});
      expect(res.status).to.eql(200);
      console.log(ids);
      console.log(res.body.translations);
      expect(res.body.translations.length).to.eql(2);// only two translations are fetched because max count = 2 unless set by env
      verifyTranslation(res.body.translations[0], 3, '1', 'pending', 'de', false, false);
      verifyTranslation(res.body.translations[1], 2, '1', 'pending', 'en', true, false);

      // pagination admin fetched translations
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          lastSent: res.body.translations[1]._id,
        });
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.eql(2);

      console.log(res.body.translations);
      console.log(ids);
      verifyTranslation(res.body.translations[0], 1, '1', 'pending', 'en', false, false);
      verifyTranslation(res.body.translations[1], 0, '1', 'pending', 'en', false, false);

      // sorting asc
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          order: 'asc',
        });
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(2);
      verifyTranslation(res.body.translations[0], 0, '1', 'pending', 'en', false, false);
      verifyTranslation(res.body.translations[1], 1, '1', 'pending', 'en', false, false);

      // limit count
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          count: 1,
        });
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(1);
      verifyTranslation(res.body.translations[0], 3, '1', 'pending', 'de', false, false);

      // language filter
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          language: 'abc',
        });
      expect(res.status).to.eql(422);

      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          language: 'es',
        });
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(0);

      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          language: 'de',
        });
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(1);
      verifyTranslation(res.body.translations[0], 3, '1', 'pending', 'de', false, false);
      const timeNow = new Date();

      // reject translation 3
      res = await request(app)
        .post('/v1/admin/translations/status')
        .set('authorization', 0)
        .send({
          id: ids[3],
          status: 'rejected',
        });
      expect(res.status).to.eql(200);
      expect(res.body).to.eql({});

      res = await fetchCoinData(1);
      // coins not awarded on rejection
      expect(res.coins).to.equal(userCoins);

      expect(totalChatMessages).to.eql(totalMessageExpected);// no new chat message received
      assert(notificationStub.notCalled);// no notification sent

      // admin fetched translations(only pending translations show)
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({});
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(2);
      verifyTranslation(res.body.translations[0], 2, '1', 'pending', 'en', true, false);
      verifyTranslation(res.body.translations[1], 1, '1', 'pending', 'en', false, false);

      // filter by status
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ status: 'rejected' });
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(1);
      verifyTranslation(res.body.translations[0], 3, '1', 'rejected', 'de', false, false);
      let reviewDate = new Date(res.body.translations[0].reviewedAt);
      expect(reviewDate).to.be.greaterThan(timeNow);
      expect(reviewDate).to.be.lessThan(new Date());

      // approve translation 1
      const approveTime2 = new Date();
      res = await request(app)
        .post('/v1/admin/translations/status')
        .set('authorization', 0)
        .send({
          id: ids[1],
          status: 'approved',
        });
      expect(res.status).to.eql(200);
      expect(res.body).to.eql({});

      userCoins += REWARD_COINS;

      // check user coins
      res = await fetchCoinData(1);
      // coins awarded on approval
      expect(res.coins).to.equal(userCoins);

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSelected);

      // notification received
      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(selectedNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      const approveTime3 = new Date();
      // approve translation 2
      res = await request(app)
        .post('/v1/admin/translations/status')
        .set('authorization', 0)
        .send({
          id: ids[2],
          status: 'approved',
        });
      expect(res.status).to.eql(200);
      expect(res.body).to.eql({});

      userCoins += REWARD_COINS;
      res = await fetchCoinData(1);
      // coins awarded on approval
      expect(res.coins).to.equal(userCoins);

      // new message
      await new Promise((r) => setTimeout(r, 100));
      totalMessageExpected++;
      expect(totalChatMessages).to.eql(totalMessageExpected);
      expect(lastSocketEventData.type).to.eql('message');
      expect(lastSocketEventData.data).to.include(supportMessageSelected);

      // notification received
      assert(notificationStub.calledOnce);
      expect(notificationStub.lastCall.args[0].notification).to.eql(selectedNotification);
      expect(notificationStub.lastCall.args[0].token).to.eql('token0');

      notificationStub.resetHistory();

      // filter by status
      // invalid input error
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ status: 'appr' });

      expect(res.status).to.equal(422);

      // asc order,approved
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ status: 'approved', order: 'asc' });
      expect(res.status).to.equal(200);
      expect(res.body.translations.length).to.equal(2);
      verifyTranslation(res.body.translations[0], 1, '1', 'approved', 'en', false, false);
      reviewDate = new Date(res.body.translations[0].reviewedAt);
      expect(res.body.translations[0].reviewedBy).to.equal('0');
      expect(reviewDate).to.be.greaterThan(approveTime2);
      expect(reviewDate).to.be.lessThan(new Date());

      console.log(ids);
      console.log(res.body.translations);
      verifyTranslation(res.body.translations[1], 2, '1', 'approved', 'en', true, false);
      reviewDate = new Date(res.body.translations[1].reviewedAt);
      expect(res.body.translations[1].reviewedBy).to.equal('0');
      expect(reviewDate).to.be.greaterThan(approveTime3);
      expect(reviewDate).to.be.lessThan(new Date());

      // upload image
      res = await request(app)
        .post('/v1/user/translation/image')
        .set('authorization', 1)
        .query({ id: ids[2] })
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      // asc order,approved
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({ status: 'approved', order: 'asc' });
      expect(res.status).to.equal(200);
      expect(res.body.translations.length).to.equal(2);
      verifyTranslation(res.body.translations[0], 1, '1', 'approved', 'en', false, false);
      reviewDate = new Date(res.body.translations[0].reviewedAt);
      expect(res.body.translations[0].reviewedBy).to.equal('0');
      expect(reviewDate).to.be.greaterThan(approveTime2);
      expect(reviewDate).to.be.lessThan(new Date());

      console.log(ids);
      console.log(res.body.translations);
      verifyTranslation(res.body.translations[1], 2, '1', 'approved', 'en', true, true);
      reviewDate = new Date(res.body.translations[1].reviewedAt);
      expect(res.body.translations[1].reviewedBy).to.equal('0');
      expect(reviewDate).to.be.greaterThan(approveTime3);
      expect(reviewDate).to.be.lessThan(new Date());

      // delete user 1's account
      user = await User.findById('1');
      await userLib.deleteAccount(user);

      // admin fetched translations(only pending show)
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({});
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.equal(1);
      verifyTranslation(res.body.translations[0], 0, null, 'pending', 'en', false, false);
      // approve translation submitted by deleted user
      res = await request(app)
        .post('/v1/admin/translations/status')
        .set('authorization', 0)
        .send({
          id: ids[0],
          status: 'approved',
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .query({
          order: 'asc',
          status: 'approved',
          count: 1,
        });
      expect(res.status).to.equal(200);
      expect(res.body.translations.length).to.equal(1);
      verifyTranslation(res.body.translations[0], 0, null, 'approved', 'en', false, false);
    });

    it('language-specific permissions', async () => {
      user = await User.findOne({ _id: 0 });
      user.adminPermissions = { translator: 'de' };
      res = await user.save();

      const ids = [];
      const pushLatestId = async () => {
        const lastTranslation = await Translation.findOne({}, { _id: 1 }).sort({ _id: -1 });
        ids.push(lastTranslation._id.valueOf());
      };

      // submit de and fr translations
      const submitTranslation = (params) => request(app)
        .post('/v1/user/submitTranslation')
        .set('authorization', 1)
        .send(params);

      res = await submitTranslation({
        currentTranslation: 'text0',
        correctTranslation: 'TEXT0',
        language: 'de',
      });
      expect(res.status).to.eql(200);
      await pushLatestId();

      res = await submitTranslation({
        currentTranslation: 'text0',
        correctTranslation: 'TEXT0',
        language: 'fr',
      });
      expect(res.status).to.eql(200);
      await pushLatestId();

      // admin fetches translations - should only get de translation
      res = await request(app)
        .get('/v1/admin/translations')
        .set('authorization', 0)
        .send({});
      expect(res.status).to.eql(200);
      expect(res.body.translations.length).to.eql(1);
      expect(res.body.translations[0]._id).to.equal(ids[0]);

      // cannot reject fr translation
      res = await request(app)
        .post('/v1/admin/translations/status')
        .set('authorization', 0)
        .send({
          id: ids[1],
          status: 'rejected',
        });
      expect(res.status).to.eql(403);

      // can reject de translation
      res = await request(app)
        .post('/v1/admin/translations/status')
        .set('authorization', 0)
        .send({
          id: ids[0],
          status: 'rejected',
        });
      expect(res.status).to.eql(200);
    });
  });

  it('update user config', async () => {
    // set "support" permissions
    user = await User.findOne({ _id: 0 });
    user.adminPermissions = { support: true };
    await user.save();

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.test_config).to.equal();

    // not permissioned
    res = await request(app)
      .patch('/v1/admin/userConfig')
      .set('authorization', 0)
      .send({
        user: '1',
        configName: 'test_config',
        configValue: true,
      });
    expect(res.status).to.equal(403);

    // grant permission
    user = await User.findOne({ _id: 0 });
    user.adminPermissions = { setConfig: true };
    res = await user.save();

    // enable config
    res = await request(app)
      .patch('/v1/admin/userConfig')
      .set('authorization', 0)
      .send({
        user: '1',
        configName: 'test_config',
        configValue: true,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.test_config).to.equal(true);

    // disable config
    res = await request(app)
      .patch('/v1/admin/userConfig')
      .set('authorization', 0)
      .send({
        user: '1',
        configName: 'test_config',
        configValue: false,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/config')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.test_config).to.equal(false);

    // invalid input
    res = await request(app)
      .patch('/v1/admin/userConfig')
      .set('authorization', 0)
      .send({
        user: '1',
        configName: 'test_config',
        configValue: '123',
      });
    expect(res.status).to.equal(422);
  });

  it('search user by email or phone number', async () => {

    await initApp('mockuser_1_testmail1_0001');
    await initApp('mockuser_2_testmail2_0002');

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: getMockEmail('testmail1') });
    expect(res.status).to.equal(200);
    expect(res.body.user._id).to.equal('1');

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: getMockEmail('testmail2') });
    expect(res.status).to.equal(200);
    expect(res.body.user._id).to.equal('2');

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: getMockNumber('0001') });
    expect(res.status).to.equal(200);
    expect(res.body.user._id).to.equal('1');

    res = await request(app)
      .get('/v1/admin/user')
      .set('authorization', 0)
      .query({ id: getMockNumber('0002') });
    expect(res.status).to.equal(200);
    expect(res.body.user._id).to.equal('2');
  });

    it(`verify profile - rejectionReason`, async () => {
      const rejectionReason = 'Incorrect pose';
      await initApp(1, { appVersion: '1.13.30' });
      await setFcmToken(1, 'token1');

      reset();
      // reject
      let res = await request(app)
        .patch('/v1/admin/verifyProfile')
        .set('authorization', 0)
        .send({
          user: '1',
          verified: false,
          rejectionReason,
        });
      expect(res.status).to.equal(rejectionReason ? 200 : 422);

      resetTime = Date.now();
      await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
      if (!rejectionReason) {
        expect(notifs.numSent).to.equal(0);
        return;
      }
      expect(notifs.recent.token).to.equal('token1');
      expect(notifs.recent.notification.title).to.equal('Verification Unsuccessful');
      expect(notifs.recent.notification.body).to.equal(`Reason: ${rejectionReason}. Please follow the profile verification guidelines.`);
      expect(notifs.recent.data).to.eql({
        verification: JSON.stringify({
          verificationStatus: 'rejected',
          verified: false,
          rejectionReason,
        }),
      });
      expect(notifs.numSent).to.equal(1);

      res = await getMyProfile(1);
      expect(res.rejectionReason).to.eql(rejectionReason);

      res = await request(app)
        .patch('/v1/admin/verifyProfile')
        .set('authorization', 0)
        .send({
          user: '1',
          verified: true,
        });

      res = await getMyProfile(1);
      expect(res.rejectionReason).to.eql(undefined);

    });

  it('test adding bannedReasons for shadow ban', async () => {
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 1 });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('bannedByAdmin');
    expect(user.bannedReasons).to.equal();

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    // ban user multiple banned reasons
    res = await request(app)
      .put('/v1/admin/ban')
      .set('authorization', 0)
      .send({
        user: '2',
        bannedReasons: ['scamming', 'creating multiple accounts'],
      });
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 2 });
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('bannedByAdmin');
    expect(user.bannedReasons).to.eql(['scamming', 'creating multiple accounts']);
  });

  it('admin should update user gender', async () => {
    let res = await request(app)
      .put('/v1/user/gender')
      .set('authorization', 1)
      .send({ gender: 'male' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/admin/userGender')
      .set('authorization', 1)
      .send({ user: '0', gender: 'female' });
    expect(res.status).to.equal(403);

    res = await request(app)
      .patch('/v1/admin/userGender')
      .set('authorization', 0)
      .send({ user: '10', gender: 'female' });
    expect(res.status).to.equal(404);

    res = await request(app)
      .patch('/v1/admin/userGender')
      .set('authorization', 0)
      .send({ gender: 'female' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/admin/userGender')
      .set('authorization', 0)
      .send({ user: '1' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/admin/userGender')
      .set('authorization', 0)
      .send({ user: '1', gender: 'random' });
    expect(res.status).to.equal(422);

    res = await request(app)
      .patch('/v1/admin/userGender')
      .set('authorization', 0)
      .send({ user: '1', gender: 'female' });
    expect(res.status).to.equal(200);

    const user1 = await User.findOne({ _id: 1 });
    expect(user1.gender).to.equal('female');
  });
});

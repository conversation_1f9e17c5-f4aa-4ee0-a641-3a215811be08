const { expect } = require('chai');
const { assert } = require('chai');
const request = require('supertest');
const sinon = require('sinon');
const { mongoose, app, validImagePath, validAudioPath, initSocket, getSocketPromise, waitMs, destroySocket } = require('./common');
const User = require('../models/user');
const Action = require('../models/action');
const Report = require('../models/report');
const PreemptiveModerationLog = require('../models/preemptive-moderation-log');
const OcrModeration = require('../models/ocr-moderation');
const BannedInfringingText = require('../models/banned-infringing-text');
const BannedEmailDomain = require('../models/banned-email-domain');
const reportLib = require('../lib/report');
const { validGoogleReceipt } = require('./iap');
const moment = require('moment');
const basic = require('../lib/basic');
const claudeClient = require('../lib/claude-client');
const Question = require('../models/question');
const Comment = require('../models/comment');
const ImageModeration = require('../models/image-moderation');
const ExtractSocialMediaHandle = require('../models/extract-social-media-handle');
const DetectInstagramSpamInImage = require('../models/detect-instagram-spam-in-image');
const constants = require('../lib/constants');
const { setMockImageModerationResponse, setMockPromptResponse, fakeAdminAuth, stubAuthEmailDomain, notifs, reset } = require('./stub');
const hive = require('../lib/hive');
const ses = require('../lib/ses');
const { BANNED_COLL_ID, UPDATED_BANNED_USERS_COLL_ID } = require('../lib/rekognition');
const BannedUser = require('../models/banned-user');
const countryLib = require('../lib/country');



const promptForOverlayText = `You are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate. identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:\n\n\n1. Nudity/Sexual Content.\n a. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.\n2. Hate Speech\n3. Spam, Promotion or Solicitation\n a. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username).\n4. Prostitution and Trafficking\n a. Selling sexual services.\n5. Scamming\n a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.\n6. Underage (below age 18)\n a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're \"18\" or \"19\", but in their bio they say things like \"17\", \"actually I'm 17\", \"17 not 18\". If a user only includes a number on their profile without other context like \"17\" or \"17 years\", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. \"my kid is 12\", \"I have a 17 year old\". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don't make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.\n\n\nIf any of the above, the user should be banned.\n\nThe person (Reporter) who reported this user (Reported User) said: Catfish/Scammer, Inappropriate Profile, \nWe don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth.\n\nIf the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says "I like sushi, gaming, and anime. My instagram handle is ig324" in their bio, then the infringing text that should be removed is "My instagram handle is ig324". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).\nFormat your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.`;

it('Replace shadow_hide with temp_shadow_ban', async () => {
  for (let i = 0; i < 4; i++) {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/education')
      .set('authorization', i)
      .send({
        education: `education${i} send me money`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', i)
      .send({
        description: `description${i}`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/work')
      .set('authorization', i)
      .send({
        work: `work${i}`,
      });
    expect(res.status).to.equal(200);
  }

  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Spam, Promotion or Solicitation',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: ['education', 'work', 'description'],
  }));

  let res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '0',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  // regular user
  let user = await User.findById('0');
  expect(user.shadowBanned).to.equal(false);
  expect(user.description).to.equal('0');
  expect(user.education).to.equal('0');
  expect(user.work).to.equal('0');
  expect(user.originalFields?.description).to.equal('description0');
  expect(user.originalFields?.education).to.equal('education0 send me money');
  expect(user.originalFields?.work).to.equal('work0');

  // report user 1
  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  // gdpr user
  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('description1');
  expect(user.education).to.equal('education1');
  expect(user.work).to.equal('work1');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');

  gdprStub.restore();

  // upload profile image for user 2
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', '2')
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  // upload another image for user 2
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', '2')
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Spam, Promotion or Solicitation',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: ['education', 'work', 'description'],
    infringingPictures: [0, 1],
  }));

  // report user 2
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '2',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 3)
    .send({
      user: '2',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('2');
  expect(user.education).to.equal('2');
  expect(user.work).to.equal('2');
  expect(user.originalFields?.description).to.equal('description2');
  expect(user.originalFields?.education).to.equal('education2 send me money');
  expect(user.originalFields?.work).to.equal('work2');
  expect(user.bannedReason).to.equal('All pictures shadow hidden');

  gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  // init user 2 again
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2);
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql(user.hiddenPictures);

  // user should be banned as temp shadow ban
  user = await User.findById('2');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('description2');
  expect(user.education).to.equal('education2 send me money');
  expect(user.work).to.equal('work2');
  expect(user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);
  expect(user.profileTempBanInfringingPictures).to.eql(user.pictures);

  // init user 0 as eu user, should be temp shadow banned
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal('education0 send me money');
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();
  gdprStub.restore();

  // add banned keywords to user 3 profile
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 3)
    .send({
      description: 'description3 send me money',
    });
  expect(res.status).to.equal(200);
  expect(res.body.description).to.equal('description3');

  // init user 3
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description3 send me money');
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.profileTempBanInfringingText).to.eql();
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();

  user = await User.findById('3');
  expect(user.shadowBanned).to.equal(false);

  const query = {
    reportedUser: '3',
    'openai.decision': 'shadow_hide',
  };
  const report = await Report.findOne(query) || await PreemptiveModerationLog.findOne(query);
  expect(report).to.equal(null);

  // init user 3 as gdpr user
  gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3);
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description3 send me money');
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();

  user = await User.findById('3');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(user.description).to.equal('description3 send me money');
  expect(user.originalFields?.description).to.equal('description3 send me money');
  expect(user.bannedBy).to.equal('banned keyword system');

  gdprStub.restore();
});

it('handles banned keywords in description for GDPR and non-GDPR users', async () => {
  const testdata = [
    'send me money',
    'venmo',
    'add me on',
    'sc',
    '2124567890',
    '212-456-7890',
    '(212)456-7890',
    '212.456.7890',
    '212 456 7890',
    '+12124567890',
    '+1 212.456.7890',
    '1-212-456-7890',
    'sam_123',
    '_123',
    'sam_',
    '@sam123',
    'sam123@',
    'sam@iam',
    '@sam_123',
    '<EMAIL>',
  ];

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // For GDPR User
  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  for (let i = 0; i < testdata.length; i++) {
    const keyword = testdata[i];
    const original = `Hello ${keyword}, hi`;

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 0)
      .send({ description: original });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(original);

    const user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
    expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
    expect(user.profileTempBanInfringingText).to.eql([keyword]);
    expect(user.description).to.equal(original);
    expect(user.originalFields?.description).to.equal(original);
    expect(user.bannedBy).to.equal('banned keyword system');

    // user banned, other user should not see the user
    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(404);
  }

  // For Non-GDPR User
  gdprStub.restore();
  for (let i = 0; i < testdata.length; i++) {
    const keyword = testdata[i];
    const original = `Hello ${keyword}, hi`;
    const redacted = 'Hello, hi';

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 1)
      .send({ description: original });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(original);

    const user = await User.findById('1');
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal(undefined);
    expect(user.profileTempBanReason).to.equal(undefined);
    expect(user.description).to.equal(redacted);
    expect(user.originalFields?.description).to.equal(original);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(redacted);
  }
});

it('handles banned keywords in education for GDPR and non-GDPR users', async () => {
  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  const original = `Hello venmo, hi`;
  const redacted = 'Hello, hi';

  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 0)
    .send({ education: original });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal(original);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal(redacted);

  // for GDPR users
  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: original });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal(original);

  // other user should not see the user
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(404);

  const user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['venmo']);
  expect(user.education).to.equal(original);
  expect(user.originalFields?.education).to.equal(original);
  gdprStub.restore();
});

it('handles banned keywords in work for GDPR and non-GDPR users', async () => {
  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  const original = `Hello venmo, hi`;
  const redacted = 'Hello, hi';

  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 0)
    .send({ work: original });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.work).to.equal(original);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.work).to.equal(redacted);

  // gdpr user
  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: original });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.work).to.equal(original);

  // other user should not see the user
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(404);

  const user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['venmo']);
  expect(user.work).to.equal(original);
  expect(user.originalFields?.work).to.equal(original);
  gdprStub.restore();
});

it('handles banned keywords in prompts for GDPR and non-GDPR users', async () => {
  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  const original = `Hello venmo, hi`;
  const redacted = 'Hello, hi';

  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  let prompts = res.body.prompts;

  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 0)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: original,
        },
      ],
    });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.prompts[0].answer).to.equal(original);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 1)
    .query({ user: '0' });
  expect(res.status).to.equal(200);
  expect(res.body.user.prompts[0].answer).to.equal(redacted);

  // gdpr user
  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  prompts = res.body.prompts;

  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: original,
        },
      ],
    });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.prompts[0].answer).to.equal(original);

  // other user should not see the user
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(404);

  const user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['venmo']);
  expect(user.prompts[0].answer).to.equal(original);
  expect(user.originalFields?.prompts[0].answer).to.equal(original);
  gdprStub.restore();
});

it('handles non banned keywords for GDPR and non-GDPR users', async () => {
  const testdata = [
    'I.E.',
    'z.B.',
    'mais...tenho',
    '6.3',
    'score',
  ];

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });
  for (let i = 0; i < testdata.length; i++) {
    const keyword = testdata[i];
    const original = `Hello ${keyword}, hi`;

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 0)
      .send({ description: original });
    expect(res.status).to.equal(200);

    // user should see the original version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(original);

    // other user should see the original version
    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(original);
  }

  gdprStub.restore();
  for (let i = 0; i < testdata.length; i++) {
    const keyword = testdata[i];
    const original = `Hello ${keyword}, hi`;

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', 1)
      .send({ description: original });
    expect(res.status).to.equal(200);

    // user should see the original version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(original);

    // other user should see the original version
    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.description).to.equal(original);
  }
});

it('handles banned keywords and infringing data in profile for gdpr users', async () => {
  let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 0)
    .send({
      education: `for chat send me money`,
    });
  expect(res.status).to.equal(200);

  let user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.education).to.equal('for chat send me money');
  expect(user.originalFields?.education).to.equal('for chat send me money');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(user.bannedBy).to.equal('banned keyword system');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal('for chat send me money');
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['send me money']);

  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Spam, Promotion or Solicitation',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: ['education'],
    infringingPictures: [],
  }));

  // remove banned keyword but add infringing text
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 0)
    .send({
      education: `education`,
    });
  expect(res.status).to.equal(200);

  // user should still be temp banned with AI reason
  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.education).to.equal('education');
  expect(user.originalFields?.education).to.equal('education');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(user.profileTempBanInfringingText).to.eql(['education']);
  expect(user.bannedBy).to.equal('openai');

  // user should not be unbanned in the middle of the process, ban history should not change
  expect(user.banHistory.length).to.equal(1);
  expect(user.banHistory[0].by).to.equal('banned keyword system');
  expect(user.banHistory[0].notes).to.equal('banned keywords found in profile - keyword: send me money');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal('education');
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['education']);

  // new report should be created
  let reports = await PreemptiveModerationLog.find({});
  expect(reports.length).to.equal(1);
  expect(reports[0].reportedUser).to.equal('0');
  expect(reports[0].reason).to.eql(['Spam']);
  expect(reports[0].openai.infringingText).to.eql(['education']);
  expect(reports[0].openai.decision).to.equal('shadow_hide');

  // remove infringing text
  setMockPromptResponse(JSON.stringify({ ban: false }));
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 0)
    .send({
      education: `some college`,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(false);
  expect(user.education).to.equal('some college');
  expect(user.originalFields?.education).to.equal('some college');
  expect(user.bannedReason).to.equal(undefined);
  expect(user.profileTempBanReason).to.equal(undefined);
  expect(user.profileTempBanInfringingText).to.eql();
  expect(user.bannedBy).to.equal(undefined);
  expect(user.banHistory.length).to.equal(2);
  expect(user.banHistory[1].action).to.equal('unban');
  expect(user.banHistory[1].notes).to.equal('undo temp shadow ban due to inappropriate profile');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.education).to.equal('some college');
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.profileTempBanInfringingText).to.eql();

  // legacy users
  gdprStub.restore();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({
      education: `for chat send me money`,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.education).to.equal('for chat');
  expect(user.originalFields?.education).to.equal('for chat send me money');
  expect(user.bannedReason).to.equal(undefined);
  expect(user.profileTempBanReason).to.equal(undefined);
  expect(user.profileTempBanInfringingText).to.eql();
  expect(user.bannedBy).to.equal(undefined);

  // add infringing text
  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Spam, Promotion or Solicitation',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: ['buy pictures'],
    infringingPictures: [],
  }));

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({
      description: `buy pictures`,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.description).to.equal('');
  expect(user.originalFields?.description).to.equal('buy pictures');
  expect(user.hiddenProfileText).to.eql(['buy pictures']);

  // login as gdpr user, user should be temp shadow banned
  gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('buy pictures');
  expect(user.originalFields?.description).to.equal('buy pictures');
  expect(user.hiddenProfileText).to.eql();
  expect(user.education).to.equal('for chat send me money');
  expect(user.originalFields?.education).to.equal('for chat send me money');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(user.profileTempBanInfringingText).to.eql(['buy pictures', 'send me money']);
  expect(user.bannedBy).to.equal('openai');
  expect(user.profileTempBanReportId).to.not.equal(undefined);
  expect(user.banHistory.length).to.equal(1);
  expect(user.banHistory[0].by).to.equal('openai');
  expect(user.banHistory[0].notes).to.equal('Spam, Promotion or Solicitation - keyword: buy pictures, send me money');

  // first remove infringing text
  setMockPromptResponse(JSON.stringify({ ban: false }));
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({
      description: `description`,
    });
  expect(res.status).to.equal(200);

  // user should still be temp shadow banned
  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('description');
  expect(user.originalFields?.description).to.equal('description');
  expect(user.education).to.equal('for chat send me money');
  expect(user.originalFields?.education).to.equal('for chat send me money');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(user.bannedBy).to.equal('banned keyword system');
  expect(user.profileTempBanReportId).to.equal(undefined);
  // ban history should not change
  expect(user.banHistory.length).to.equal(1);
  expect(user.banHistory[0].by).to.equal('openai');
  expect(user.banHistory[0].notes).to.equal('Spam, Promotion or Solicitation - keyword: buy pictures, send me money');

  // user will be unbanned after removing banned keywords
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({
      education: `some college`,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.education).to.equal('some college');
  expect(user.originalFields?.education).to.equal('some college');
  expect(user.bannedReason).to.equal(undefined);
  expect(user.profileTempBanReason).to.equal(undefined);
  expect(user.profileTempBanInfringingText).to.eql();
  expect(user.bannedBy).to.equal(undefined);
  expect(user.profileTempBanReportId).to.equal(undefined);
  expect(user.banHistory.length).to.equal(2);
  expect(user.banHistory[1].action).to.equal('unban');
  expect(user.banHistory[1].notes).to.equal('undo temp shadow ban due to inappropriate profile');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2);
  expect(res.status).to.equal(200);

  // add some images to user 1
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  // report user 1
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  // flag images as inappropriate
  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Spam, Promotion or Solicitation',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: [],
    infringingPictures: [1],
  }));

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  // user 1 should be temp shadow banned
  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(user.profileTempBanInfringingText).to.eql();
  expect(user.profileTempBanInfringingPictures).to.eql([user.pictures[1]]);
  expect(user.bannedBy).to.equal('openai');
  expect(user.profileTempBanReportId).to.not.equal(undefined);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql();
  expect(res.body.user.profileTempBanInfringingPictures).to.eql([user.pictures[1]]);

  // add banned keyword to user profile
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({
      description: `hello, send me money`,
    });
  expect(res.status).to.equal(200);

  // user still shadow banned with AI reason but banned keyword is also added
  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('hello, send me money');
  expect(user.originalFields?.description).to.equal('hello, send me money');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(user.bannedBy).to.equal('openai');
  expect(user.profileTempBanReportId).to.not.equal(undefined);
  expect(user.profileTempBanInfringingPictures).to.eql([user.pictures[1]]);
  expect(user.profileTempBanInfringingText).to.eql(['send me money']);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('hello, send me money');
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql([user.pictures[1]]);

  // remove infringing picture and get ban false response from AI
  setMockPromptResponse(JSON.stringify({ ban: false }));

  res = await request(app)
    .post('/v1/user/editPicture')
    .set('authorization', 1)
    .query({ id: user.pictures[1] })
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  await waitMs(100);

  // user should still be temp shadow banned without infringing picture but with banned keyword
  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.description).to.equal('hello, send me money');
  expect(user.originalFields?.description).to.equal('hello, send me money');
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('banned keywords found in profile');
  expect(user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(user.bannedBy).to.equal('banned keyword system');
  expect(user.profileTempBanReportId).to.equal(undefined);
  expect(user.profileTempBanInfringingPictures).to.eql();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('hello, send me money');
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['send me money']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();

  // removing banned keyword should unban user
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({
      description: `hello, hi`,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.description).to.equal('hello, hi');
  expect(user.originalFields?.description).to.equal('hello, hi');
  expect(user.bannedReason).to.equal(undefined);
  expect(user.profileTempBanReason).to.equal(undefined);
  expect(user.profileTempBanInfringingText).to.eql();
  expect(user.bannedBy).to.equal(undefined);
  expect(user.profileTempBanReportId).to.equal(undefined);
  expect(user.profileTempBanInfringingPictures).to.eql();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('hello, hi');
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.profileTempBanInfringingText).to.eql();
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();
});

it('temp ban should not replace permanent ban', async () => {
  setMockPromptResponse('{"ban": true, "reason": "scam"}');

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 0)
    .send({
      education: `send me money for education`,
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 1)
    .send({
      user: '0',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Scamming');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_ban');

  let user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedBy).to.equal('openai');
  expect(user.bannedReason).to.equal('Scamming');
  expect(user.originalFields?.education).to.equal('send me money for education');
  expect(user.education).to.equal('for education');

  gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
    includes: () => true,
  });

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.profileTempBanInfringingText).to.eql();
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();

  // ban is not override with temp ban
  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('Scamming');
  expect(user.profileTempBanReason).to.eql();
  expect(user.profileTempBanInfringingText).to.eql();

  // add more infringing text to profile
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({
      description: `paypal`,
    });
  expect(res.status).to.equal(200);

  // ban is not override with temp ban
  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('Scamming');
  expect(user.profileTempBanReason).to.eql();
  expect(user.profileTempBanInfringingText).to.eql();
});

describe('APP-878: Fix user temp banned multiple times with the same reason and notes', () => {
  beforeEach(async () => {
    for (let i = 0; i < 2; i++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', i);
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/education')
        .set('authorization', i)
        .send({
          education: `education${i} send me money`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/description')
        .set('authorization', i)
        .send({
          description: `description${i}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app)
        .put('/v1/user/work')
        .set('authorization', i)
        .send({
          work: `work${i}`,
        });
      expect(res.status).to.equal(200);
    }
  });

  it('should not apply duplicate temp ban for extra whitespaces', async () => {
    setMockPromptResponse(JSON.stringify({
      ban: true,
      reason: 'spam',
      explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
      infringingText: ['education', 'work', 'description'],
    }));

    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // regular user
    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.description).to.equal('0');
    expect(user.education).to.equal('0');
    expect(user.work).to.equal('0');
    expect(user.originalFields?.description).to.equal('description0');
    expect(user.originalFields?.education).to.equal('education0 send me money');
    expect(user.originalFields?.work).to.equal('work0');

    let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
      includes: () => true,
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // gdpr user
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.description).to.equal('description0');
    expect(user.education).to.equal('education0 send me money');
    expect(user.work).to.equal('work0');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
    expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);

    // update original education field with white spaces
    user.originalFields.education = ' education0 send me money ';
    user.originalFields.description = 'just paypal ';
    user.description = 'just paypal';
    await user.save();

    // init again, original fields should match with profile fields, should not run temp ban again
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    //  paypal will not be added in infringing text
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);
    expect(user.bannedBy).to.equal('openai');
    const previousInfringingText = user.profileTempBanInfringingText;

    // change description
    user.description = 'description0';
    await user.save();
    previousInfringingText.push('paypal');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.description).to.equal('just paypal');
    expect(user.bannedBy).to.equal('openai');

    let profileTexts = `${user.firstName || ''} ${user.education || ''} ${user.work || ''} ${user.description || ''}`;
    for (let i = 0; i < (user.prompts || []).length; i++) {
      profileTexts = `${profileTexts} ${user.prompts[i].answer || ''}`;
    }
    const lowerProfileText = profileTexts.toLowerCase();
    let filteredInfringingText = previousInfringingText.filter(t => lowerProfileText.includes(t.toLowerCase()));

    // description should be filtered out, it does not exist in user profile anymore
    expect(filteredInfringingText).to.not.include('description');
    expect(user.profileTempBanInfringingText.sort()).to.eql(filteredInfringingText.sort());

    gdprStub.restore();
  });

  it('should not apply temp ban if last openai followup ban is false', async () => {
    setMockPromptResponse(JSON.stringify({
      ban: true,
      reason: 'spam',
      explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
      infringingText: ['education', 'work', 'description'],
    }));

    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // regular user
    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.description).to.equal('0');
    expect(user.education).to.equal('0');
    expect(user.work).to.equal('0');
    expect(user.originalFields?.description).to.equal('description0');
    expect(user.originalFields?.education).to.equal('education0 send me money');
    expect(user.originalFields?.work).to.equal('work0');

    let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
      includes: () => true,
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // gdpr user
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.description).to.equal('description0');
    expect(user.education).to.equal('education0 send me money');
    expect(user.work).to.equal('work0');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
    expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);

    // update education
    res = await request(app)
      .put('/v1/user/education')
      .set('authorization', 0)
      .send({
        education: 'education0',
      });
    expect(res.status).to.equal(200);

    // send me money should be removed from profileTempBanInfringingText
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description']);

    // shadow ban false
    setMockPromptResponse(JSON.stringify({
      ban: false,
    }));

    // initapp should not remove ban
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanInfringingText).to.eql(['education', 'work', 'description']);

    // update work
    res = await request(app)
      .put('/v1/user/work')
      .set('authorization', 0)
      .send({
        work: 'work update',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.work).to.equal('work update');
    expect(user.profileTempBanInfringingText).to.eql();

    // mismatch field with original field
    user.originalFields.work = 'work0';
    user.work = 'work update';
    await user.save();

    const query = {
      reportedUser: '0',
      'openai.decision': 'shadow_hide',
    };
    let report = await Report.findOne(query).sort({ createdAt: -1 });
    if (!report) {
      report = await PreemptiveModerationLog.findOne(query).sort({ createdAt: -1 });
    }

    expect(report).to.not.equal(null);
    expect(report.openai.ban).to.equal(true);
    expect(report.openaiFollowUp.length).to.equal(2);
    expect(report.openaiFollowUp[1].ban).to.equal(false);

    // initApp should restore the original field, latest follow up is false, no banned keywords, so user should not be banned
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.work).to.equal('work0');
    expect(res.body.user.profileTempBanInfringingText).to.eql();

    gdprStub.restore();
  });

  it('should fix temp ban for affected users', async () => {
    setMockPromptResponse(JSON.stringify({
      ban: true,
      reason: 'spam',
      explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
      infringingText: ['education', 'work', 'description'],
    }));

    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // regular user
    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.description).to.equal('0');
    expect(user.education).to.equal('0');
    expect(user.work).to.equal('0');
    expect(user.originalFields?.description).to.equal('description0');
    expect(user.originalFields?.education).to.equal('education0 send me money');
    expect(user.originalFields?.work).to.equal('work0');

    let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
      includes: () => true,
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // gdpr user
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.description).to.equal('description0');
    expect(user.education).to.equal('education0 send me money');
    expect(user.work).to.equal('work0');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
    expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);
    expect(user.bannedBy).to.equal('openai');

    // shadow ban false
    setMockPromptResponse(JSON.stringify({
      ban: false,
    }));

    // update eucation
    res = await request(app)
      .put('/v1/user/education')
      .set('authorization', 0)
      .send({
        education: `education update`,
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.profileTempBanInfringingText).to.eql();

    let report = await Report.findOne({
      reportedUser: '0',
      'openai.decision': 'shadow_hide',
    });

    // condition setup for affected users
    user.shadowBanned = true;
    user.profileTempBanInfringingText = report.openai.infringingText;
    user.bannedBy = 'openai';
    user.bannedReason = 'temp shadow ban due to inappropriate profile';
    user.profileTempBanReason = 'Spam, Promotion or Solicitation';
    user.profileTempBanReportId = report._id;
    await user.save();

    // initApp should fix the profile because last follow up of this report is ban false
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanInfringingText).to.eql();

    user = await User.findById('0');
    expect(user.profileTempBanInfringingText).to.eql();
    expect(user.shadowBanned).to.equal(false);
    expect(user.bannedReason).to.equal();
    expect(user.profileTempBanReason).to.equal();
    expect(user.profileTempBanReportId).to.equal();

    gdprStub.restore();
  });

  it('should fix temp ban for users with no follow up/non existent infringing content', async () => {
    setMockPromptResponse(JSON.stringify({
      ban: true,
      reason: 'spam',
      explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
      infringingText: ['education', 'work', 'description'],
    }));

    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    // regular user
    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);
    expect(user.description).to.equal('0');
    expect(user.education).to.equal('0');
    expect(user.work).to.equal('0');
    expect(user.originalFields?.description).to.equal('description0');
    expect(user.originalFields?.education).to.equal('education0 send me money');
    expect(user.originalFields?.work).to.equal('work0');

    let gdprStub = sinon.stub(countryLib, 'gdprCountries').value({
      includes: () => true,
    });

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // gdpr user
    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.description).to.equal('description0');
    expect(user.education).to.equal('education0 send me money');
    expect(user.work).to.equal('work0');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
    expect(user.profileTempBanInfringingText).to.eql(['education', 'work', 'description', 'send me money']);
    expect(user.bannedBy).to.equal('openai');

    // remove all infringing text from users profile
    user.originalFields.work = 'IT';
    user.originalFields.education = 'some school';
    user.originalFields.description = 'updated bio';
    await user.save();

    let reports = await Report.find();
    expect(reports.length).to.equal(1);
    expect(reports[0].status).to.equal('verified');
    expect(reports[0].openai.ban).to.equal(true);
    expect(reports[0].openaiFollowUp?.length).to.equal(0);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanInfringingText).to.equal(undefined);
    expect(res.body.user.bannedReason).to.equal(undefined);

    gdprStub.restore();
  });
});

describe('banned face update', () => {
  beforeEach(async () => {
    const payload = {
      img: 'base-64-image-data',
      secure: {
        version: "2.7.0",
        token: "token-data",
        verification: "verification-data",
        signature: "signature-data",
      },
    };

    for (let uid = 0; uid < 3; uid++) {
      let res = await request(app)
        .put('/v1/user/initApp')
        .set('authorization', uid)
        .send({ appVersion: '1.13.65' });
      expect(res.status).to.equal(200);

      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', uid)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);

      // should verify the profile
      res = await request(app)
        .post('/v1/user/profileVerificationPicture/liveness')
        .set('authorization', uid)
        .send(payload);
      expect(res.status).to.equal(200);
      expect(res.body.verificationStatus).to.equal('verified');
      expect(res.body.rejectionReason).to.equal(undefined);
    }
  });

  it('report user 0 for scamming', async () => {
    setMockPromptResponse('{"ban": true, "reason": "scam"}');
    const previousColl = [];
    const newColl = [];
    fakeRekognition.indexFaces = function (params) {
      if (params.CollectionId === BANNED_COLL_ID) {
        previousColl.push(params.Image.S3Object.Name);
      }
      if (params.CollectionId === UPDATED_BANNED_USERS_COLL_ID) {
        newColl.push(params.Image.S3Object.Name);
      }

      const impl = function (resolve, reject) {
        console.log('Fake indexFaces');
        console.log(params);
        resolve({
          FaceRecords: [{ Face: { FaceId: params.Image.S3Object.Name } }],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    // report
    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    const reports = await Report.find();
    expect(reports.length).to.equal(1);
    console.log(reports[0]);
    expect(reports[0].status).to.equal('verified');
    expect(reports[0].openai.ban).to.equal(true);
    expect(reports[0].openai.banReason).to.equal('Scamming');
    expect(reports[0].openai.violationLocation).to.equal('profile');
    expect(reports[0].openai.decision).to.equal('shadow_ban');

    const user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('Scamming');

    // User is verified, so only verification image should be added to new banned collection
    expect(previousColl.length).to.equal(0); // no previous banned collection
    expect(newColl.length).to.equal(1);

    const bannedUser = await BannedUser.findOne({ user: '0' });
    expect(bannedUser.faceIds.length).to.equal(1);
  });

  it('no face should be added for unverified user', async () => {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 3)
      .send({ appVersion: '1.13.65' });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 3)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    setMockPromptResponse('{"ban": true, "reason": "underage"}');
    const previousColl = [];
    const newColl = [];
    fakeRekognition.indexFaces = function (params) {
      if (params.CollectionId === BANNED_COLL_ID) {
        previousColl.push(params.Image.S3Object.Name);
      }
      if (params.CollectionId === UPDATED_BANNED_USERS_COLL_ID) {
        newColl.push(params.Image.S3Object.Name);
      }

      const impl = function (resolve, reject) {
        console.log('Fake indexFaces');
        console.log(params);
        resolve({
          FaceRecords: [{ Face: { FaceId: params.Image.S3Object.Name } }],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    // report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '3',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    const reports = await Report.find();
    expect(reports.length).to.equal(1);
    expect(reports[0].status).to.equal('verified');
    expect(reports[0].openai.ban).to.equal(true);
    expect(reports[0].openai.banReason).to.equal('Underage');
    expect(reports[0].openai.decision).to.equal('shadow_ban');

    const user = await User.findById('3');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('Underage');

    // User is banned but not verified, so no face should be added to new banned collection
    expect(previousColl.length).to.equal(0); // no previous banned collection
    expect(newColl.length).to.equal(0);

    const bannedUser = await BannedUser.findOne({ user: '3' });
    expect(bannedUser.faceIds.length).to.equal(0);
  });

  it('test banned face delete from rekognition', async () => {
    setMockPromptResponse('{"ban": true, "reason": "scam"}');
    const previousColl = [];
    let newColl = [];
    fakeRekognition.indexFaces = function (params) {
      if (params.CollectionId === BANNED_COLL_ID) {
        previousColl.push(params.Image.S3Object.Name);
      }
      if (params.CollectionId === UPDATED_BANNED_USERS_COLL_ID) {
        newColl.push(params.Image.S3Object.Name);
      }

      const impl = function (resolve, reject) {
        console.log('Fake indexFaces');
        console.log(params);
        resolve({
          FaceRecords: [{ Face: { FaceId: params.Image.S3Object.Name } }],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    fakeRekognition.deleteFaces = function (params) {
      const impl = function (resolve, reject) {
        console.log('Fake deleteFaces');
        console.log(params);
        newColl = newColl.filter((f) => !params.FaceIds.includes(f));
        resolve({
          DeletedFaces: [params.FaceIds],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    // report
    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    const reports = await Report.find();
    expect(reports.length).to.equal(1);
    console.log(reports[0]);
    expect(reports[0].status).to.equal('verified');
    expect(reports[0].openai.ban).to.equal(true);
    expect(reports[0].openai.banReason).to.equal('Scamming');
    expect(reports[0].openai.violationLocation).to.equal('profile');
    expect(reports[0].openai.decision).to.equal('shadow_ban');

    let user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('Scamming');

    expect(previousColl.length).to.equal(0); // no previous banned collection
    expect(newColl.length).to.equal(1);

    let bannedUser = await BannedUser.findOne({ user: '0' });
    expect(bannedUser.faceIds.length).to.equal(1);

    // create admin user
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    user = await User.findOne({ _id: 2 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app)
      .put('/v1/admin/unban')
      .set('authorization', 2)
      .send({
        user: '0',
        notes: 'not scammer',
      });
    expect(res.status).to.equal(200);

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(false);

    // User is unbanned, so all images should be deleted from BannedUser collection and rekognition collection
    bannedUser = await BannedUser.findOne({ user: '0' });
    expect(bannedUser.faceIds).to.deep.equal([]);
    expect(newColl.length).to.equal(0);
  });

  it('temp shadow ban - save profile data but do not save banned faces', async () => {
    setMockPromptResponse('{"ban": true, "reason": "nudity"}');
    const previousColl = [];
    const newColl = [];
    fakeRekognition.indexFaces = function (params) {
      if (params.CollectionId === BANNED_COLL_ID) {
        previousColl.push(params.Image.S3Object.Name);
      }
      if (params.CollectionId === UPDATED_BANNED_USERS_COLL_ID) {
        newColl.push(params.Image.S3Object.Name);
      }

      const impl = function (resolve, reject) {
        console.log('Fake indexFaces');
        console.log(params);
        resolve({
          FaceRecords: [{ Face: { FaceId: params.Image.S3Object.Name } }],
        });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    // report
    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Profile'],
        comment: 'Inappropriate Profile',
      });
    expect(res.status).to.equal(200);

    const reports = await Report.find();
    expect(reports.length).to.equal(1);
    console.log(reports[0]);
    expect(reports[0].status).to.equal('verified');
    expect(reports[0].openai.ban).to.equal(true);
    expect(reports[0].openai.banReason).to.equal('Nudity/Sexual Content');
    expect(reports[0].openai.violationLocation).to.equal('profile');
    expect(reports[0].openai.decision).to.equal('temp_shadow_ban');

    const user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');

    // nothing added to banned collection
    expect(previousColl.length).to.equal(0);
    expect(newColl.length).to.equal(0);

    const bannedUser = await BannedUser.findOne({ user: '0' });
    console.log(bannedUser);
    expect(bannedUser.faceIds.length).to.equal(0);
  });
});

async function isShadowBanned(uid) {
  return (await User.findOne({ _id: uid })).shadowBanned;
}

describe('report moderation update', () => {
  beforeEach(async () => {
    let res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 2);
    expect(res.status).to.equal(200);
  });

  it('Verify text only report processing uses Claude as previous', async () => {
    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    let reports = await Report.find();
    expect(reports.length).to.equal(1);
    expect(reports[0].createdAt).to.eql(reports[0].updatedAt);

    expect(reports[0].openai.provider).to.equal('anthropic');
    expect(reports[0].openai.model).to.equal('claude-sonnet-4-20250514');

    // report with user 2
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    reports = await Report.find({}).sort({ createdAt: -1 });
    expect(reports.length).to.equal(2);
    expect(reports[0].openai.provider).to.equal('anthropic');
    expect(reports[0].openai.model).to.equal('claude-sonnet-4-20250514');
  });

  it('Verify image attached report processing with GPT within48Hours', async () => {
    // upload profile image for user 1
    let res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', '1')
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    const moderation = await ImageModeration.find({});
    expect(moderation.length).to.equal(1);
    expect(moderation[0].moderationLabels.length).to.equal(0); // No, yes_overlay_text

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    let reports = await Report.find();
    expect(reports.length).to.equal(1);
    expect(reports[0].createdAt).to.eql(reports[0].updatedAt);
    // first report should be processed with claude (text only prompt)
    expect(reports[0].openai.provider).to.equal('anthropic');
    expect(reports[0].openai.model).to.equal('claude-sonnet-4-20250514');

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 2)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    const userData = await User.findOne({ _id: '1' });

    const expectedPromptObj = {
      prompt: `\nYou are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate, and based on their entire profile, including work, identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:\n\n\n1. Nudity/Sexual Content.\n    a. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.\n2. Hate Speech\n3. Spam, Promotion or Solicitation\n    a. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username). Additionally, check for attempts to circumvent detection by separating characters in the social media handle with spaces or special characters, such as “c i a r r a w h i s p p e r” or “c_i_a_r_r_a_w_h_i_s_p_p_e_r”.\n4. Prostitution and Trafficking\n    a. Selling sexual services.\n5. Scamming\n    a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.\n6. Underage (below age 18)\n    a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're \"18\" or \"19\", but in their bio they say things like \"17\", \"actually I'm 17\", \"17 not 18\". If a user only includes a number on their profile without other context like \"17\" or \"17 years\", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. \"my kid is 12\", \"I have a 17 year old\". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don’t make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.\n\n\nIf any of the above, the user should be banned.\n\nThe person (Reporter) who reported this user (Reported User) said: spam, spam\nWe don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth:\n\nReported User Profile Data:\nName: \nSelf-reported Age: undefined\nPrevious or Current Education Level or Institution (tip: saying \"high school\" or \"middle school\" doesn't mean they're underage, but may be indicating that was their highest level of education attainment): \nWork: undefined\nBio: \nProfile Pictures: see attached pictures.\n\nGiven the evidence provided, decide whether to ban or dismiss the Reported User and provide an explanation for your decision. Make sure to not take the Reporter’s comment as absolute truth, but instead to base this decision off the evidence presented in the Reported User's profile. If the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says \"I like sushi, gaming, and anime. My instagram handle is ig324\" in their bio, then the infringing text that should be removed is \"My instagram handle is ig324\". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).\n\nFormat your response as a json object in the following format: { \"ban\": true/false, \"reason\": reason, \"explanation\": explanation, \"infringingText\": [\"text\"], \"infringingPictures\": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.\n`,
      imageUrls: userData?.pictures.map((p) => `${constants.IMAGE_DOMAIN}${p}`),
    };

    reports = await Report.find({}).sort({ createdAt: -1 });
    expect(reports.length).to.equal(2);
    // second report should be processed with gpt and previous prompt, because user has image
    expect(reports[0].openai.provider).to.equal('openai');
    expect(reports[0].openai.model).to.equal('gpt-4o-mini');
    expect(reports[0].openai.prompt).to.equal(JSON.stringify(expectedPromptObj, null, 2));
  });

  it('Verify labeled image report processing with updated prompt and gpt', async () => {
    // upload profile image for user 1
    let res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', '1')
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    response = {
      isFlagged: false,
      detectionLabels: [{
        ParentName: '',
        Name: 'yes_overlay_text',
        Confidence: 95,
      }],
      flaggedModerationLabel: {},
    };
    setMockImageModerationResponse(response);

    // upload another image for user 1
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', '1')
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    response = {
      isFlagged: false,
      detectionLabels: [{
        ParentName: '',
        Name: 'text',
        Confidence: 95,
      }],
      flaggedModerationLabel: {},
    };
    setMockImageModerationResponse(response);

    // upload another image for user 1
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', '1')
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);

    const userData = await User.findOne({ _id: '1' });
    expect(userData.pictures.length).to.equal(3);

    const moderation = await ImageModeration.find({}).sort({ createdAt: -1 });
    expect(moderation.length).to.equal(3);
    expect(moderation[0].moderationLabels.length).to.equal(1);
    expect(moderation[0].moderationLabels[0].Name).to.equal('text');
    expect(moderation[1].moderationLabels.length).to.equal(1);
    expect(moderation[1].moderationLabels[0].Name).to.equal('yes_overlay_text');
    expect(moderation[2].moderationLabels.length).to.equal(0);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    let reports = await Report.find();
    expect(reports.length).to.equal(1);

    // one of the image has yes_overlay_text label, so should be processed with gpt
    expect(reports[0].openai.provider).to.equal('openai');
    expect(reports[0].openai.model).to.equal('gpt-4o-mini');

    const expectedPromptObj = {
      prompt: `You are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate. identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:\n\n\n1. Nudity/Sexual Content.\n a. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.\n2. Hate Speech\n3. Spam, Promotion or Solicitation\n a. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username).\n4. Prostitution and Trafficking\n a. Selling sexual services.\n5. Scamming\n a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.\n6. Underage (below age 18)\n a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're \"18\" or \"19\", but in their bio they say things like \"17\", \"actually I'm 17\", \"17 not 18\". If a user only includes a number on their profile without other context like \"17\" or \"17 years\", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. \"my kid is 12\", \"I have a 17 year old\". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don't make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.\n\n\nIf any of the above, the user should be banned.\n\nThe person (Reporter) who reported this user (Reported User) said: Catfish/Scammer, Inappropriate Profile, \nWe don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth.\n\nIf the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says "I like sushi, gaming, and anime. My instagram handle is ig324" in their bio, then the infringing text that should be removed is "My instagram handle is ig324". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).\nFormat your response as a json object in the following format: { "ban": true/false, "reason": reason, "explanation": explanation, "infringingText": ["text"], "infringingPictures": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.`,
      imageUrls: [
        `${constants.IMAGE_DOMAIN}${userData?.pictures[1]}`,
        `${constants.IMAGE_DOMAIN}${userData?.pictures[2]}`,
      ], // images flagged with text or yes_overlay_text should be sent
    };

    expect(reports[0].openai.prompt).to.equal(JSON.stringify(expectedPromptObj, null, 2));

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 2)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    const previousPromptObj = {
      prompt: `\nYou are a moderator for a social media and dating app called Boo. Your job is to review users reported by other users as being inappropriate, and based on their entire profile, including work, identify whether the Reported User is engaging in any of the below activities that would violate our community guidelines:\n\n\n1. Nudity/Sexual Content.\n    a. Nudity and extremely disrespectful sexual content should be banned. But users should be allowed to express their sexuality and their sexual desires and preferences and what they're looking for in their profile.\n2. Hate Speech\n3. Spam, Promotion or Solicitation\n    a. Don't include any form of domain name, links or URLs in your bio that take to other websites. Don't include your social media handles or say to follow or message you on another platform outside of Boo. Don't promote a specific event or company, non-profit, political campaign, contest, or research. Please don't use Boo to promote yourself or your events. Any mentions of social media handles, email, or phone numbers should be immediately banned (e.g. whatsapp, IG, snap, discord, telegram, @username, user_name, follow me @username). Additionally, check for attempts to circumvent detection by separating characters in the social media handle with spaces or special characters, such as “c i a r r a w h i s p p e r” or “c_i_a_r_r_a_w_h_i_s_p_p_e_r”.\n4. Prostitution and Trafficking\n    a. Selling sexual services.\n5. Scamming\n    a. Blackmailing or trying to collect payments or money from other users. Tip: If the Reporter is accusing the Reported User of using photos of someone else, like someone famous, don't believe the Reporter. Having a non-facial photo without a face in their profile is okay and is not enough evidence alone of scamming and should not be banned.\n6. Underage (below age 18)\n    a. Users ages 1 to 17 should be banned. Oftentimes these users lie in the self-reported age section of their profile that they're \"18\" or \"19\", but in their bio they say things like \"17\", \"actually I'm 17\", \"17 not 18\". If a user only includes a number on their profile without other context like \"17\" or \"17 years\", then it's likely they are underage as they are likely to be referring to their age. Make sure not to ban people who are referring to the age of their children, e.g. \"my kid is 12\", \"I have a 17 year old\". Don't assume they're underage just because they say they're in high school because it's possible to be in high school and of 18 years old. In general, don’t make assumptions; only ban the account only if you are 100% certain the user is underage; refrain from banning if there is uncertainty. Do not ban if the number is related to time (e.g: seconds, minutes, hours). Do not ban if the number is related to measurement (e.g: inches, ft, km). Do not ban if the context provided pertains to the past.\n\n\nIf any of the above, the user should be banned.\n\nThe person (Reporter) who reported this user (Reported User) said: spam, spam\nWe don't know if that's true or not, here's the evidence we need to evaluate below to determine if the Reporter is telling the truth:\n\nReported User Profile Data:\nName: \nSelf-reported Age: undefined\nPrevious or Current Education Level or Institution (tip: saying \"high school\" or \"middle school\" doesn't mean they're underage, but may be indicating that was their highest level of education attainment): \nWork: undefined\nBio: \nProfile Pictures: see attached pictures.\n\nGiven the evidence provided, decide whether to ban or dismiss the Reported User and provide an explanation for your decision. Make sure to not take the Reporter’s comment as absolute truth, but instead to base this decision off the evidence presented in the Reported User's profile. If the result is ban, also provide the reason for the ban as well as the infringing text and/or pictures that should be removed from the profile in order to make it compliant. For infringing text, output ONLY the contiguous words and ensure that the profile is still coherent and grammatical if these words were removed from the profile. Example: If a user says \"I like sushi, gaming, and anime. My instagram handle is ig324\" in their bio, then the infringing text that should be removed is \"My instagram handle is ig324\". For infringing pictures, provide the indices of the pictures (assume zero-based indexing).\n\nFormat your response as a json object in the following format: { \"ban\": true/false, \"reason\": reason, \"explanation\": explanation, \"infringingText\": [\"text\"], \"infringingPictures\": [0] }, where reason must be one of the following: Nudity/Sexual Content; Hate Speech; Spam, Promotion or Solicitation; Prostitution and Trafficking; Scamming; Underage. Your response should contain just the JSON with no additional description, context, or markdown.\n`,
      imageUrls: userData?.pictures.map((p) => `${constants.IMAGE_DOMAIN}${p}`), // all images will be sent
    };

    reports = await Report.find({}).sort({ createdAt: -1 });
    expect(reports.length).to.equal(2);
    // second report should be processed with gpt and previous prompt
    expect(reports[0].openai.provider).to.equal('openai');
    expect(reports[0].openai.model).to.equal('gpt-4o-mini');
    expect(reports[0].openai.prompt).to.equal(JSON.stringify(previousPromptObj, null, 2));

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 3);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 3)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    reports = await Report.find({}).sort({ createdAt: -1 });
    expect(reports.length).to.equal(3);
    // third report within 48 hours should be processed with gpt and previous prompt
    expect(reports[0].openai.provider).to.equal('openai');
    expect(reports[0].openai.model).to.equal('gpt-4o-mini');
    expect(reports[0].openai.prompt).to.equal(JSON.stringify(previousPromptObj, null, 2));

    // update all reports createdAt to 49 hours ago
    await Report.updateMany({}, { $set: { createdAt: moment().subtract(49, 'hours').toDate() } });

    res = await request(app)
      .get('/v1/user')
      .set('authorization', 4);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 4)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    reports = await Report.find({}).sort({ createdAt: -1 });
    expect(reports.length).to.equal(4);
    // 48 hours passed till last report, so will be processed with new prompt and only labeled image
    expect(reports[0].openai.provider).to.equal('openai');
    expect(reports[0].openai.model).to.equal('gpt-4o-mini');
    expect(reports[0].openai.prompt).to.equal(JSON.stringify(expectedPromptObj, null, 2));
  });

  it('Verify extractSocialMediaHandles using claude-sonnet-4', async () => {
    constants.runPreemptiveModeration.restore();
    sinon.stub(constants, 'runPreemptiveModeration').returns(true);

    setMockPromptResponse(JSON.stringify({
      ban: true,
      reason: 'Spam, Promotion or Solicitation',
      explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
      infringingText: ['work'],
    }));

    let res = await request(app)
      .put('/v1/user/work')
      .set('authorization', 0)
      .send({
        work: `some work`,
      });
    expect(res.status).to.equal(200);

    let reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
    expect(reports[0].openai.socialMediaHandles).to.eql([]);

    reports = await ExtractSocialMediaHandle.find().sort('-createdAt').limit(1);
    expect(reports.length).to.equal(1);
    expect(reports[0].model).to.eql('claude-sonnet-4-20250514');
  });
});

describe('test audio transcription', () => {
  let fakeClaudeClient, createSpy;
  beforeEach(async () => {
    for (let uid = 0; uid < 3; uid++) {
      let res = await request(app).put('/v1/user/initApp').set('authorization', uid);
      expect(res.status).to.equal(200);
    }
    fakeClaudeClient = {
      messages: {
        async create(params) {
          return {
            usage: {
              input_tokens: 10,
              output_tokens: 10,
            },
            content: [
              {
                text: JSON.stringify({ ban: false }),
              },
            ],
          };
        },
      },
    };
    claudeClient.getClaudeApiClient.restore();
    sinon.stub(claudeClient, 'getClaudeApiClient').returns(fakeClaudeClient);
    createSpy = sinon.spy(fakeClaudeClient.messages, 'create');
  });

  afterEach(() => {
    claudeClient.getClaudeApiClient.restore();
    createSpy.restore();
  });

  it('should add audio transcription if exists to prompt when user is being reported', async () => {
    let res = await request(app)
      .post('/v1/user/audioDescription')
      .set('authorization', 0)
      .attach('audio', validAudioPath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(200);

    const user = await User.findOne({ _id: '0' });
    expect(user.audioDescriptionTranscription).to.equal('mock transcription');

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);
    let args = createSpy.getCall(0).args[0];
    expect(createSpy.calledOnce).to.equal(true);
    expect(args?.messages[0]?.content[0]?.text?.includes('\nAI Transcript of audio from profile: mock transcription')).to.equal(true);

    // User 1 does not have audioDescriptionTranscription, so it should not be added to the prompt
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 2)
      .send({
        user: '1',
        reason: ['Spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);
    args = createSpy.getCall(1).args[0];
    expect(args?.messages[0]?.content[0]?.text?.includes('\nAI Transcript of audio from profile: mock transcription')).to.equal(false);
  });

  it('should add audio transcription if exists to prompt when post is being reported', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
        mediaUploadPending: true,
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // add audio to the post
    res = await request(app)
      .post('/v1/question/audio')
      .set('authorization', 0)
      .query({ questionId: q1Id })
      .attach('audio', validAudioPath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(200);

    // create another post with user 0
    res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'second post',
      });
    expect(res.status).to.equal(200);
    const q2Id = res.body._id;

    const post = await Question.findById(q1Id);
    expect(post.audioTranscription).to.equal('mock transcription');

    const post2 = await Question.findById(q2Id);
    expect(post2.audioTranscription).to.equal(undefined);

    // report post 1 with user 1
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q1Id });
    expect(res.status).to.equal(200);

    let args = createSpy.getCall(0).args[0];
    expect(createSpy.calledOnce).to.equal(true);
    expect(args?.messages[0]?.content[0]?.text?.includes('\nAI Transcript of audio from post: mock transcription')).to.equal(true);

    // report second post with user 1
    res = await request(app)
      .patch('/v1/question/report')
      .set('authorization', 1)
      .send({ questionId: q2Id });
    expect(res.status).to.equal(200);

    // This should not add audio transcription to prompt
    args = createSpy.getCall(1).args[0];
    expect(args?.messages[0]?.content[0]?.text?.includes('\nAI Transcript of audio from profile: mock transcription')).to.equal(false);
  });

  it('should add audio transcription if exists to prompt when comment is being reported', async () => {
    let res = await request(app)
      .post('/v1/question')
      .set('authorization', 0)
      .send({
        interestName: 'chess',
        title: 'first post',
      });
    expect(res.status).to.equal(200);
    const q1Id = res.body._id;

    // user 1 posts empty comment
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', 1)
      .send({
        questionId: q1Id,
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c1Id = res.body._id;

    // add audio to comment
    res = await request(app)
      .post('/v1/comment/audio')
      .set('authorization', 1)
      .query({ commentId: c1Id })
      .attach('audio', validAudioPath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(200);

    // create another comment with user 2
    res = await request(app)
      .post('/v1/comment')
      .set('authorization', '2')
      .send({
        questionId: q1Id,
        text: 'Comment 1',
        parentId: q1Id,
      });
    expect(res.status).to.equal(200);
    const c2Id = res.body._id;

    const comment1 = await Comment.findById(c1Id);
    expect(comment1.audioTranscription).to.equal('mock transcription');

    const comment2 = await Comment.findById(c2Id);
    expect(comment2.audioTranscription).to.equal(undefined);

    // report comment 1 with user 0
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 0)
      .send({ commentId: c1Id });
    expect(res.status).to.equal(200);

    let args = createSpy.getCall(0).args[0];
    expect(createSpy.calledOnce).to.equal(true);
    expect(args?.messages[0]?.content[0]?.text?.includes('AI Transcript of audio from comment: mock transcription')).to.equal(true);

    // report second comment with user 1
    res = await request(app)
      .patch('/v1/comment/report')
      .set('authorization', 1)
      .send({ commentId: c2Id });
    expect(res.status).to.equal(200);

    // This should not add audio transcription to prompt
    args = createSpy.getCall(1).args[0];
    expect(args?.messages[0]?.content[0]?.text?.includes('\nAI Transcript of audio from profile: mock transcription')).to.equal(false);
  });
});

describe('testing shadow ban and needs review added new user', async () => {
  beforeEach(async () => {
    for (let uid = 0; uid < 5; uid++) {
      let res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
    }
  });

  it('handle underage reports manually', async () => {
    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 3)
      .send({
        user: '4',
        reason: ['Underage'],
        comment: 'looks too young',
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: '0' });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedUser).to.equal('4');
    expect(res.body.reports[0].status).to.equal('needs review');
  });

  it('handle catfish/scammer reports manually', async () => {
    let res = await request(app)
      .post('/v1/report')
      .set('authorization', 3)
      .send({
        user: '4',
        reason: ['Catfish/Scammer'],
        comment: 'scammer',
      });
    expect(res.status).to.equal(200);

    let user = await User.findOne({ _id: '0' });
    user.admin = true;
    user.adminPermissions = { support: true };
    res = await user.save();

    res = await request(app)
      .get('/v1/admin/reports')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    expect(res.body.reports[0].reportedUser).to.equal('4');
    expect(res.body.reports[0].status).to.equal('needs review');
  });
});

describe('report', () => {
  it('valid report', async () => {
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    const reports = await Report.find();
    expect(reports.length).to.equal(1);
    expect(reports[0].createdAt).to.eql(reports[0].updatedAt);
  });

  it('invalid report', async () => {
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(404);
  });

  it('invalid report', async () => {
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: 'spam',
        comment: 'spam',
      });
    expect(res.status).to.equal(422);
  });

  it('discard No Facial Photo report', async () => {
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['No Facial Photo'],
        comment: 'comment',
      });
    expect(res.status).to.equal(200);

    reports = await Report.find();
    expect(reports.length).to.equal(0);

    // multiple reasons - not discarded
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['No Facial Photo', 'Spam'],
        comment: 'comment',
      });
    expect(res.status).to.equal(200);

    reports = await Report.find();
    expect(reports.length).to.equal(1);
  });

  /*
  it('reset num pending reports', async () => {
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    res = await request(app)
      .get('/v1/user')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['spam'],
        comment: 'spam',
      });
    expect(res.status).to.equal(200);

    expect((await User.findById('0')).metrics.numPendingReports).to.equal(0);
    expect((await User.findById('1')).metrics.numPendingReports).to.equal(1);

    user = await User.findById('0');
    user.metrics.numPendingReports = 1;
    await user.save();

    expect((await User.findById('0')).metrics.numPendingReports).to.equal(1);

    await reportLib.resetNumPendingReports();

    expect((await User.findById('0')).metrics.numPendingReports).to.equal(0);
    expect((await User.findById('1')).metrics.numPendingReports).to.equal(1);
  });
  */
});

it('automated report - chat deleted', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // create empty deleted chat
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/reject')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  // create new chat with messages
  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 1)
    .send({
      user: '0',
      text: 'msg2',
    });
  expect(res.status).to.equal(200);

  // delete the chat
  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Spam'],
      comment: 'Spam',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
});

it('inappropriate messages - with images', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message/image')
    .query({ recipient: 1 })
    .set('authorization', 0)
    .attach('image', validImagePath)
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
});

it('inappropriate messages - without images', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
});

it('inappropriate messages - dismissed', async () => {
  setMockPromptResponse('{"ban": false}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].status).to.equal('dismissed');
  expect(reports[0].openai.ban).to.equal(false);
  expect(reports[0].openai.decision).to.equal('dismiss');

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.tempBanReason).to.equal();
  expect(user.profileTempBanReason).to.equal();
});

it('inappropriate messages - trafficking', async () => {
  setMockPromptResponse('{"ban": true, "reason": "Trafficking"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Prostitution and Trafficking');
  expect(reports[0].openai.violationLocation).to.equal('messages');
  expect(reports[0].openai.decision).to.equal('shadow_ban');

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedBy).to.equal('openai');
  expect(user.bannedReason).to.equal('Prostitution and Trafficking');
});

it('inappropriate messages - meanness', async () => {
  setMockPromptResponse('{"ban": true, "reason": "mean and rude"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  // send some messages from both users
  res = await request(app)
    .post('/v1/message')
    .set('authorization', 1)
    .send({
      user: '0',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg2',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message/image')
    .set('authorization', 1)
    .query({ recipient: 0 })
    .attach('image', validImagePath)
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Meanness or Rudeness');
  expect(reports[0].openai.violationLocation).to.equal('messages');
  expect(reports[0].openai.decision).to.equal('temp_ban');

  user = await User.findById('1');
  console.log(JSON.stringify(user.banHistory,null,2));
  expect(user.banHistory.length).to.equal(1);
  expect(user.banHistory[0].action).to.equal('tempBan');
  expect(user.banHistory[0].evidence.messages.length).to.equal(2);
  expect(user.banHistory[0].evidence.messages[0].image).to.include('1/evidence/');
  expect(user.banHistory[0].evidence.messages[1].text).to.equal('msg1');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.11.45' });
  expect(res.status).to.equal(200);
  expect(res.body.user.tempBanReason).to.equal('Meanness or Rudeness');
  expect(res.body.user.tempBanEndAt).to.not.equal(undefined);
});

it('inappropriate profile - error', async () => {
  setMockPromptResponse('Ban scam');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].status).to.equal('dismissed');
  expect(reports[0].openai.ban).to.equal();
  expect(reports[0].openai.decision).to.equal('dismiss');

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.tempBanReason).to.equal();
  expect(user.profileTempBanReason).to.equal();
});

it('inappropriate profile - scam', async () => {
  setMockPromptResponse('{"ban": true, "reason": "scam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Scamming');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_ban');

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedBy).to.equal('openai');
  expect(user.bannedReason).to.equal('Scamming');
});

it('inappropriate profile - spam', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_hide');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
});

it('other - profile - spam', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Other'],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_hide');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
});

it('other - messages - spam', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "messages"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 0)
    .send({
      user: '1',
    });
  expect(res.status).to.equal(200);
  res = await request(app)
    .patch('/v1/user/approve')
    .set('authorization', 1)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/message')
    .set('authorization', 0)
    .send({
      user: '1',
      text: 'msg1',
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Other'],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('messages');
  expect(reports[0].openai.decision).to.equal('temp_ban');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.11.45' });
  expect(res.status).to.equal(200);
  expect(res.body.user.tempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.tempBanEndAt).to.not.equal(undefined);
});

it('ignore no facial photo', async () => {
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Other', 'No Facial Photo'],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].reason).to.eql(['Other']);
});

it('shadow ban if 5 temp bans from 5 different reporters within 30 days', async () => {
  setMockPromptResponse('{"ban": true, "reason": "mean and rude"}');

  async function createMatch(i) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', 0)
      .send({
        user: i.toString(),
      });
    expect(res.status).to.equal(200);
    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', i)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: i.toString(),
        text: 'msg1',
      });
    expect(res.status).to.equal(200);
  }

  clock = sinon.useFakeTimers();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  await createMatch(1);

  // 5 reports from the same user - temp ban only
  for (let i = 0; i < 5; i++) {
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 1)
      .send({
        user: '0',
        reason: ['Inappropriate Messages'],
        comment: 'Inappropriate Messages',
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.45' });
  expect(res.status).to.equal(200);
  expect(res.body.user.tempBanReason).to.equal('Meanness or Rudeness');
  expect(res.body.user.tempBanEndAt).to.not.equal(undefined);

  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(false);

  // 2 days - temp ban expires
  clock.tick(2 * 24 * 3600 * 1000);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.45' });
  expect(res.status).to.equal(200);
  expect(res.body.user.tempBanReason).to.equal();
  expect(res.body.user.tempBanEndAt).to.equal();

  // report again - no temp ban
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 1)
    .send({
      user: '0',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.45' });
  expect(res.status).to.equal(200);
  expect(res.body.user.tempBanReason).to.equal();
  expect(res.body.user.tempBanEndAt).to.equal();

  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(false);

  // 30 days
  clock.tick(30 * 24 * 3600 * 1000);

  // reports from 4 different users - temp ban only
  for (let i = 2; i < 6; i++) {
    await createMatch(i);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', i)
      .send({
        user: '0',
        reason: ['Inappropriate Messages'],
        comment: 'Inappropriate Messages',
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.11.45' });
  expect(res.status).to.equal(200);
  expect(res.body.user.tempBanReason).to.equal('Meanness or Rudeness');
  expect(res.body.user.tempBanEndAt).to.not.equal(undefined);

  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(false);

  // shadow banned on the 5th report
  await createMatch(6);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 6)
    .send({
      user: '0',
      reason: ['Inappropriate Messages'],
      comment: 'Inappropriate Messages',
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
});

it('undo temp shadow ban', async () => {
  setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Nudity/Sexual Content');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('temp_shadow_ban');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);

  // now dismiss
  setMockPromptResponse('{"ban": false}');

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'clean' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
});

it('override tempban infringing data', async () => {
  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: "Nudity/Sexual Content",
    explanation: "Inappropriate Profile",
    infringingText: ["nude"],
  }));

  let res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  const socket0 = await initSocket(1);
  let socketPromise = getSocketPromise(socket0, 'profileTempBan');

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  await waitMs(100);
  res = await socketPromise;
  expect(res).to.eql({
    profileTempBanReason: 'Nudity/Sexual Content',
    profileTempBanInfringingText: ['nude'],
  });

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Nudity/Sexual Content');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('temp_shadow_ban');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
  expect(res.body.user.profileTempBanInfringingText).to.eql(["nude"]);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', '1')
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  let user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
  expect(user.profileTempBanInfringingText).to.eql(['nude']);
  const pictureKey = user.pictures[0];

  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: "Nudity/Sexual Content",
    explanation: "Inappropriate Profile",
    infringingText: ["some bad text"],
    infringingPictures: [0],
  }));
  socketPromise = getSocketPromise(socket0, 'profileTempBan');

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'some bad text' });
  expect(res.status).to.equal(200);

  await waitMs(100);
  res = await socketPromise;
  expect(res).to.eql({
    profileTempBanReason: 'Nudity/Sexual Content',
    profileTempBanInfringingText: ['some bad text'],
    profileTempBanInfringingPictures: [pictureKey],
  });

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.profileTempBanReason).to.equal('Nudity/Sexual Content');
  expect(user.profileTempBanInfringingText).to.eql(["some bad text"]);
  expect(user.profileTempBanInfringingPictures).to.eql([pictureKey]);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Nudity/Sexual Content');
  expect(res.body.user.profileTempBanInfringingText).to.eql(["some bad text"]);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql([pictureKey]);

  await destroySocket(socket0);
});

it('limits', async () => {
  // limit 10 user reports per 24 hours
  // ignore if report success ratio over past 30 days is less than 10%

  setMockPromptResponse('{"ban": false}');

  clock = sinon.useFakeTimers();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  // 10 reports from the same user
  for (let i = 1; i <= 10; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: i.toString(),
        reason: ['Inappropriate Profile'],
      });
    expect(res.status).to.equal(200);

    reports = await Report.find();
    expect(reports.length).to.equal(i);
  }

  // next report is ignored
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 11);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '11',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  reports = await Report.find();
  expect(reports.length).to.equal(10);

  // a different user can still report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 1)
    .send({
      user: '11',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  reports = await Report.find().sort('-_id');
  expect(reports.length).to.equal(11);
  expect(reports[0].reportedBy).to.equal('1');

  // 24 hours
  clock.tick(1 * 24 * 3600 * 1000);

  // still not allowed because report success ratio is less than 10%
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 12);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '12',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  reports = await Report.find().sort('-_id');
  expect(reports.length).to.equal(11);

  // 30 days
  clock.tick(30 * 24 * 3600 * 1000);

  // next report is allowed
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 12);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '12',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  reports = await Report.find().sort('-_id');
  expect(reports.length).to.equal(12);
  expect(reports[0].reportedBy).to.equal('0');

  clock.restore();
});

it('verified account with infringing text', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["example", "2"], "infringingPictures": []}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.verification.status = 'verified';
  await user.save();

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  const { prompts } = res.body;
  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2',
        },
        {
          id: prompts[1].id,
          answer: 'clean',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2',
        },
      ],
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_hide');
  expect(reports[0].openai.infringingText).to.eql(['example', '2']);
  expect(reports[0].openai.infringingPictures).to.eql();
  expect(reports[0].openai.infringingTextFoundInName).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.description).to.equal('description example');
  expect(res.body.user.work).to.equal('work example');
  expect(res.body.user.education).to.equal('education example');
  expect(res.body.user.prompts[0].answer).to.equal('prompt example 2');
  expect(res.body.user.prompts[1].answer).to.equal('clean');
  expect(res.body.user.prompts[2].answer).to.equal('clean 2');

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description');
  expect(res.body.user.work).to.equal('work');
  expect(res.body.user.education).to.equal('education');
  expect(res.body.user.prompts[0].answer).to.equal('prompt');
  expect(res.body.user.prompts[1].answer).to.equal('clean');
  expect(res.body.user.prompts[2].answer).to.equal('clean');

  // edit profile - infringing text should be removed
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example 3' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example 3' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example 3' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2 3',
        },
        {
          id: prompts[1].id,
          answer: 'clean 3',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2 3',
        },
      ],
    });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.description).to.equal('description example 3');
  expect(res.body.user.work).to.equal('work example 3');
  expect(res.body.user.education).to.equal('education example 3');
  expect(res.body.user.prompts[0].answer).to.equal('prompt example 2 3');
  expect(res.body.user.prompts[1].answer).to.equal('clean 3');
  expect(res.body.user.prompts[2].answer).to.equal('clean 2 3');

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description  3');
  expect(res.body.user.work).to.equal('work  3');
  expect(res.body.user.education).to.equal('education  3');
  expect(res.body.user.prompts[0].answer).to.equal('prompt   3');
  expect(res.body.user.prompts[1].answer).to.equal('clean 3');
  expect(res.body.user.prompts[2].answer).to.equal('clean  3');
});

it('unverified account with infringing text', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["example", "2"], "infringingPictures": []}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  const { prompts } = res.body;
  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2',
        },
        {
          id: prompts[1].id,
          answer: 'clean',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2',
        },
      ],
    });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_hide');
  expect(reports[0].openai.infringingText).to.eql(['example', '2']);
  expect(reports[0].openai.infringingPictures).to.eql();
  expect(reports[0].openai.infringingTextFoundInName).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.description).to.equal('description example');
  expect(res.body.user.work).to.equal('work example');
  expect(res.body.user.education).to.equal('education example');
  expect(res.body.user.prompts[0].answer).to.equal('prompt example 2');
  expect(res.body.user.prompts[1].answer).to.equal('clean');
  expect(res.body.user.prompts[2].answer).to.equal('clean 2');

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description');
  expect(res.body.user.work).to.equal('work');
  expect(res.body.user.education).to.equal('education');
  expect(res.body.user.prompts[0].answer).to.equal('prompt');
  expect(res.body.user.prompts[1].answer).to.equal('clean');
  expect(res.body.user.prompts[2].answer).to.equal('clean');

  // edit profile - infringing text should be removed
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example 3' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example 3' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example 3' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2 3',
        },
        {
          id: prompts[1].id,
          answer: 'clean 3',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2 3',
        },
      ],
    });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.description).to.equal('description example 3');
  expect(res.body.user.work).to.equal('work example 3');
  expect(res.body.user.education).to.equal('education example 3');
  expect(res.body.user.prompts[0].answer).to.equal('prompt example 2 3');
  expect(res.body.user.prompts[1].answer).to.equal('clean 3');
  expect(res.body.user.prompts[2].answer).to.equal('clean 2 3');

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description  3');
  expect(res.body.user.work).to.equal('work  3');
  expect(res.body.user.education).to.equal('education  3');
  expect(res.body.user.prompts[0].answer).to.equal('prompt   3');
  expect(res.body.user.prompts[1].answer).to.equal('clean 3');
  expect(res.body.user.prompts[2].answer).to.equal('clean  3');
});

it('preemptive moderation - spam not found', async () => {
  constants.runPreemptiveModeration.restore();
  sinon.stub(constants, 'runPreemptiveModeration').returns(true);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": false}');
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.reportedUser).to.equal('1');
  expect(report.status).to.equal('dismissed');
  expect(report.openai.ban).to.equal(false);
  expect(report.openai.banReason).to.equal();
  expect(report.openai.violationLocation).to.equal('profile');
  expect(report.openai.decision).to.equal('dismiss');
  expect(report.openai.infringingText).to.eql();
  expect(report.openai.infringingPictures).to.eql();
  expect(report.openai.infringingTextFoundInName).to.equal();

  setMockPromptResponse('{"ban": true, "reason": "Underage", "violationLocation": "profile", "infringingText": [], "infringingPictures": []}');
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.reportedUser).to.equal('1');
  expect(report.status).to.equal('dismissed');
  expect(report.openai.ban).to.equal(true);
  expect(report.openai.banReason).to.equal('Underage');
  expect(report.openai.violationLocation).to.equal('profile');
  expect(report.openai.decision).to.equal('dismiss');
  expect(report.openai.infringingText).to.eql();
  expect(report.openai.infringingPictures).to.eql();
  expect(report.openai.infringingTextFoundInName).to.equal();

  setMockPromptResponse('{"ban": true, "reason": "Nudity/Sexual Content", "violationLocation": "profile", "infringingText": ["placeholder"], "infringingPictures": []}');
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.reportedUser).to.equal('1');
  expect(report.status).to.equal('dismissed');
  expect(report.openai.ban).to.equal(true);
  expect(report.openai.banReason).to.equal('Nudity/Sexual Content');
  expect(report.openai.violationLocation).to.equal('profile');
  expect(report.openai.decision).to.equal('dismiss');
  expect(report.openai.infringingText).to.eql(['placeholder']);
  expect(report.openai.infringingPictures).to.eql();
  expect(report.openai.infringingTextFoundInName).to.equal();

  setMockPromptResponse('{"ban": true, "reason": "Hate Speech", "violationLocation": "profile", "infringingText": ["placeholder"], "infringingPictures": []}');
  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  const { prompts } = res.body;
  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2',
        },
        {
          id: prompts[1].id,
          answer: 'clean',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2',
        },
      ],
    });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.reportedUser).to.equal('1');
  expect(report.status).to.equal('dismissed');
  expect(report.openai.ban).to.equal(true);
  expect(report.openai.banReason).to.equal('Hate Speech');
  expect(report.openai.violationLocation).to.equal('profile');
  expect(report.openai.decision).to.equal('dismiss');
  expect(report.openai.infringingText).to.eql(['placeholder']);
  expect(report.openai.infringingPictures).to.eql();
  expect(report.openai.infringingTextFoundInName).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
});

it('preemptive moderation - spam found', async () => {
  constants.runPreemptiveModeration.restore();
  sinon.stub(constants, 'runPreemptiveModeration').returns(true);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["example", "2"], "infringingPictures": []}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  const { prompts } = res.body;
  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2',
        },
        {
          id: prompts[1].id,
          answer: 'clean',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2',
        },
      ],
    });
  expect(res.status).to.equal(200);

  const reports = await PreemptiveModerationLog.find();
  expect(reports.length).to.equal(4);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_hide');
  expect(reports[0].openai.infringingText).to.eql(['example', '2']);
  expect(reports[0].openai.infringingPictures).to.eql();
  expect(reports[0].openai.infringingTextFoundInName).to.equal();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
  expect(user.bannedNotes).to.equal();

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.description).to.equal('description example');
  expect(res.body.user.work).to.equal('work example');
  expect(res.body.user.education).to.equal('education example');
  expect(res.body.user.prompts[0].answer).to.equal('prompt example 2');
  expect(res.body.user.prompts[1].answer).to.equal('clean');
  expect(res.body.user.prompts[2].answer).to.equal('clean 2');

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description');
  expect(res.body.user.work).to.equal('work');
  expect(res.body.user.education).to.equal('education');
  expect(res.body.user.prompts[0].answer).to.equal('prompt');
  expect(res.body.user.prompts[1].answer).to.equal('clean');
  expect(res.body.user.prompts[2].answer).to.equal('clean');

  // edit profile - infringing text should be removed
  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example 3' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example 3' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example 3' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2 3',
        },
        {
          id: prompts[1].id,
          answer: 'clean 3',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2 3',
        },
      ],
    });
  expect(res.status).to.equal(200);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.description).to.equal('description example 3');
  expect(res.body.user.work).to.equal('work example 3');
  expect(res.body.user.education).to.equal('education example 3');
  expect(res.body.user.prompts[0].answer).to.equal('prompt example 2 3');
  expect(res.body.user.prompts[1].answer).to.equal('clean 3');
  expect(res.body.user.prompts[2].answer).to.equal('clean 2 3');

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.description).to.equal('description  3');
  expect(res.body.user.work).to.equal('work  3');
  expect(res.body.user.education).to.equal('education  3');
  expect(res.body.user.prompts[0].answer).to.equal('prompt   3');
  expect(res.body.user.prompts[1].answer).to.equal('clean 3');
  expect(res.body.user.prompts[2].answer).to.equal('clean  3');
});

it('preemptive moderation - run on verified accounts', async () => {
  constants.runPreemptiveModeration.restore();
  sinon.stub(constants, 'runPreemptiveModeration').returns(true);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["example", "2"], "infringingPictures": []}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  user.verification.status = 'verified';
  await user.save();

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/work')
    .set('authorization', 1)
    .send({ work: 'work example' });
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/education')
    .set('authorization', 1)
    .send({ education: 'education example' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .get('/v1/user/profilePrompts')
    .set('authorization', 1);
  expect(res.status).to.equal(200);
  const { prompts } = res.body;
  res = await request(app)
    .put('/v1/user/profilePromptAnswers')
    .set('authorization', 1)
    .send({
      prompts: [
        {
          id: prompts[0].id,
          answer: 'prompt example 2',
        },
        {
          id: prompts[1].id,
          answer: 'clean',
        },
        {
          id: prompts[2].id,
          answer: 'clean 2',
        },
      ],
    });
  expect(res.status).to.equal(200);

  const reports = await PreemptiveModerationLog.find();
  expect(reports.length).to.equal(4);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
});

it('preemptive moderation - check model', async () => {
  constants.runPreemptiveModeration.restore();
  sinon.stub(constants, 'runPreemptiveModeration').returns(true);
  setMockPromptResponse('{"ban": false}');

  // Fine tuned gpt-4o-mini model will be used for preemptive user profile text moderation

  // signup source is web
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({os: 'web'})
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  expect(report.reportedUser).to.equal('0');
  expect(report.openai.model).to.equal('ft:gpt-4o-mini-2024-07-18:boo-enterprises:v2:Azg5gCcU');
  expect(report.openai.reasoning_content).to.equal();

  response = {
    isFlagged: false,
    detectionLabels: [{
      ParentName: '',
      Name: 'yes_overlay_text',
      Confidence: 95,
    }],
    flaggedModerationLabel: {},
  };
  setMockImageModerationResponse(response);
  setMockPromptResponse('{"ban": false}');

  // signup source is not web
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({os: 'ios'})
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 1)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  expect(report.reportedUser).to.equal('1');
  expect(report.openai.model).to.equal('ft:gpt-4o-mini-2024-07-18:boo-enterprises:v2:Azg5gCcU');
});

it('preemptive moderation - infringing text undefined', async () => {
  constants.runPreemptiveModeration.restore();
  sinon.stub(constants, 'runPreemptiveModeration').returns(true);
  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["undefined"], "infringingPictures": []}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.reportedUser).to.equal('0');
  expect(report.openai.decision).to.equal('dismiss');
  expect(report.openai.infringingText).to.eql();

  user = await User.findById('0');
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
});

it('extract social media handles for banned infringing text', async () => {
  constants.runPreemptiveModeration.restore();
  sinon.stub(constants, 'runPreemptiveModeration').returns(true);

  // mock openai to return infringing text
  fakeOpenaiClient.chat = {
    completions: {
      async create(params) {
        console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
        return {
          usage: {
            prompt_tokens: 10,
            completion_tokens: 10,
          },
          choices: [{
            message: {
              content: '{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["mock", "handle"], "infringingPictures": []}'
            },
          }],
        }
      }
    }
  }

  // mock claude to return invalid json
  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      return {
        usage: {
          input_tokens: 10,
          output_tokens: 10,
        },
        content: [{
          text: 'NOT_FOUND'
        }],
      }
    }
  }

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.openai.socialMediaHandles).to.eql([]);

  reports = await ExtractSocialMediaHandle.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.inputText).to.equal('mock handle');
  expect(report.output).to.equal('NOT_FOUND');
  expect(report.socialMediaHandles).to.eql([]);
  expect(report.explanation).to.equal();
  expect(report.isError).to.equal(true);
  expect(report.errorMessage).to.equal('json parsing error: Unexpected token N in JSON at position 0');

  // mock claude to return valid json with no handle
  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      return {
        usage: {
          input_tokens: 10,
          output_tokens: 10,
        },
        content: [{
          text: `{"handle": "","explanation": "No social media handles were found in the text."}`
        }],
      }
    }
  }

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.openai.socialMediaHandles).to.eql([]);

  reports = await ExtractSocialMediaHandle.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.inputText).to.equal('mock handle');
  expect(report.output).to.equal(`{"handle": "","explanation": "No social media handles were found in the text."}`);
  expect(report.socialMediaHandles).to.eql([]);
  expect(report.explanation).to.equal('No social media handles were found in the text.');
  expect(report.isError).to.equal(false);
  expect(report.errorMessage).to.equal();

  // mock claude to return valid json with handle
  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      return {
        usage: {
          input_tokens: 10,
          output_tokens: 10,
        },
        content: [{
          text: `{"handle": "handle","explanation": "A handle was found."}`
        }],
      }
    }
  }

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.openai.socialMediaHandles).to.eql(['handle']);

  reports = await ExtractSocialMediaHandle.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.inputText).to.equal('mock handle');
  expect(report.output).to.equal(`{"handle": "handle","explanation": "A handle was found."}`);
  expect(report.socialMediaHandles).to.eql(['handle']);
  expect(report.explanation).to.equal('A handle was found.');
  expect(report.isError).to.equal(false);
  expect(report.errorMessage).to.equal();

  // mock claude to return valid json with multiple handles
  fakeClaudeClient.messages = {
    async create(params) {
      console.log(`Fake claude completions: ${JSON.stringify(params)}`);
      return {
        usage: {
          input_tokens: 10,
          output_tokens: 10,
        },
        content: [{
          text: `{"handle": ["handle1", "handle2"],"explanation": "A handle was found."}`
        }],
      }
    }
  }

  await mongoose.connection.db.dropDatabase();

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/description')
    .set('authorization', 0)
    .send({ description: 'description example' });
  expect(res.status).to.equal(200);

  reports = await PreemptiveModerationLog.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.openai.socialMediaHandles).to.eql(['handle1', 'handle2']);

  reports = await ExtractSocialMediaHandle.find().sort('-createdAt').limit(1);
  report = reports[0];
  console.log(report);
  expect(report.inputText).to.equal('mock handle');
  expect(report.output).to.equal(`{"handle": ["handle1", "handle2"],"explanation": "A handle was found."}`);
  expect(report.socialMediaHandles).to.eql(['handle1', 'handle2']);
  expect(report.explanation).to.equal('A handle was found.');
  expect(report.isError).to.equal(false);
  expect(report.errorMessage).to.equal();
});

it('infringing text found in name', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": ["example", "2"], "infringingPictures": []}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', 1)
    .send({ firstName: 'name example' });
  expect(res.status).to.equal(200);

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('temp_shadow_ban');
  expect(reports[0].openai.infringingText).to.eql(['example', '2']);
  expect(reports[0].openai.infringingPictures).to.eql();
  expect(reports[0].openai.infringingTextFoundInName).to.equal(true);

  // banned
  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.bannedNotes).to.include('Spam, Promotion or Solicitation - keyword: example, 2');
  console.log(user.banHistory);

  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(404);

  // user is temp banned
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(res.body.user.firstName).to.equal('name example');

  // unban
  user = await User.findOne({ _id: 0 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  res = await request(app)
    .put('/v1/admin/unban')
    .set('authorization', 0)
    .send({
      user: '1',
      notes: 'not scammer',
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();

  // user should be banned again for the same infringing text
  res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', 1)
    .send({ firstName: 'name example example' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Spam'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');

  report = await Report.findOne().sort('-createdAt');
  console.log(report);

  res = await request(app)
    .put('/v1/admin/unban')
    .set('authorization', 0)
    .send({
      user: '1',
      notes: 'not scammer',
    });
  expect(res.status).to.equal(200);

  // can still be re-banned for different infringing text
  res = await request(app)
    .put('/v1/user/firstName')
    .set('authorization', 1)
    .send({ firstName: 'name 2' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Spam'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);

  report = await Report.findOne().sort('-createdAt');
  console.log(report);
});

describe('infringing pictures', async () => {
  let originalPictures, redactedPictures;

  beforeEach(async () => {
    // first report does not process pictures
    setMockPromptResponse('{"ban": false}');
    setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0, 2]}');

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 2);
    expect(res.status).to.equal(200);

    for (let i = 0; i < 4; i++) {
      res = await request(app)
        .post('/v1/user/picture/v2')
        .set('authorization', 1)
        .attach('image', validImagePath);
      expect(res.status).to.equal(200);
      originalPictures = res.body.pictures;
    }
    redactedPictures = [ originalPictures[1], originalPictures[3] ];

    // report
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['Inappropriate Profile'],
      });
    expect(res.status).to.equal(200);

    // first report only process text data, second report will process pictures
    setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0, 2]}');
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 2)
      .send({
        user: '1',
        reason: ['Inappropriate Profile'],
      });
    expect(res.status).to.equal(200);

    const reports = await Report.find().sort('-createdAt');
    expect(reports.length).to.equal(2);
    console.log(reports[0]);
    //expect(reports[0].status).to.equal('verified');
    expect(reports[0].openai.ban).to.equal(true);
    expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
    expect(reports[0].openai.violationLocation).to.equal('profile');
    expect(reports[0].openai.decision).to.equal('shadow_hide');
    expect(reports[0].openai.infringingText).to.eql();
    expect(reports[0].openai.infringingPictures).to.eql([0,2]);

    user = await User.findById('1');
    expect(user.shadowBanned).to.equal(false);

    // user should see the original version
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.profileTempBanReason).to.equal();
    expect(res.body.user.pictures).to.eql(originalPictures);

    // other user should see the redacted version
    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('add new picture', async () => {
    res = await request(app)
      .post('/v1/user/picture/v2')
      .set('authorization', 1)
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.pictures.length).to.equal(5);
    originalPictures.push(res.body.pictures[4]);
    redactedPictures.push(res.body.pictures[4]);
    expect(res.body.pictures).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('edit hidden picture', async () => {
    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 1)
      .query({ id: originalPictures[0] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    originalPictures[0] = res.body[0];
    redactedPictures.splice(0, 0, res.body[0]);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('edit non-hidden picture', async () => {
    res = await request(app)
      .post('/v1/user/editPicture')
      .set('authorization', 1)
      .query({ id: originalPictures[1] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    originalPictures[1] = res.body[1];
    redactedPictures[0] = res.body[1];
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('reorder hidden picture', async () => {
    originalPictures = [originalPictures[2], originalPictures[0], originalPictures[1], originalPictures[3]];
    res = await request(app)
      .put('/v1/user/reorderPictures')
      .set('authorization', 1)
      .send({ ids: originalPictures })
    expect(res.status).to.equal(200);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('reorder non-hidden picture', async () => {
    originalPictures = [originalPictures[0], originalPictures[2], originalPictures[3], originalPictures[1]];
    redactedPictures = [redactedPictures[1], redactedPictures[0]];
    res = await request(app)
      .put('/v1/user/reorderPictures')
      .set('authorization', 1)
      .send({ ids: originalPictures })
    expect(res.status).to.equal(200);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('delete hidden picture', async () => {
    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 1)
      .query({ id: originalPictures[0] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    originalPictures.splice(0, 1);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('delete non-hidden picture', async () => {
    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 1)
      .query({ id: originalPictures[1] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    originalPictures.splice(1, 1);
    redactedPictures.splice(0, 1);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(redactedPictures);
  });

  it('delete all non-hidden pictures - shadow ban', async () => {
    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 1)
      .query({ id: originalPictures[3] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    originalPictures.splice(3, 1);
    redactedPictures.splice(1, 1);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .delete('/v1/user/picture')
      .set('authorization', 1)
      .query({ id: originalPictures[1] })
      .attach('image', validImagePath);
    expect(res.status).to.equal(200);
    originalPictures.splice(1, 1);
    redactedPictures.splice(0, 1);
    expect(res.body).to.eql(originalPictures);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(404);

    user = await User.findById('1');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal(null);
    expect(user.bannedReason).to.equal('All pictures shadow hidden');
  });

  it('all remaining pictures hidden - shadow ban', async () => {
    setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0, 1]}');

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['Inappropriate Profile'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(404);

    user = await User.findById('1');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal(null);
    expect(user.bannedReason).to.equal('All pictures shadow hidden');
  });

  it('reverify if first picture hidden - lose verification', async () => {
    const firstPicture = redactedPictures[0];
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        if (params.TargetImage.S3Object.Name == firstPicture) {
          resolve({ FaceMatches: [{}] });
        } else {
          resolve({ UnmatchedFaces: [{}] });
        }
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    // get verified
    const payload = {
      img: 'base-64-image-data',
      secure: {
        version: "2.7.0",
        token: "token-data",
        verification: "verification-data",
        signature: "signature-data",
      },
    };

    let res = await request(app)
      .post('/v1/user/profileVerificationPicture/liveness')
      .set('authorization', 1)
      .send(payload);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();

    setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0]}');

    // get first picture hidden - user will lose verification
    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['Inappropriate Profile'],
      });
    expect(res.status).to.equal(200);

    redactedPictures = [redactedPictures[1]];

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('rejected');
    expect(res.body.user.rejectionReason).to.equal('Make sure your first profile picture is a picture of you, and only you.');

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.body.user.pictures).to.eql(redactedPictures);
    expect(res.body.user.verified).to.equal(false);
    expect(res.body.user.verificationStatus).to.equal('unverified');
  });

  it('reverify if first picture hidden - keep verification', async () => {
    fakeRekognition.compareFaces = function (params) {
      const impl = function (resolve, reject) {
        resolve({ FaceMatches: [ {} ] });
      };
      return {
        promise: () => new Promise(impl),
      };
    };

    // get verified
    const payload = {
      img: 'base-64-image-data',
      secure: {
        version: "2.7.0",
        token: "token-data",
        verification: "verification-data",
        signature: "signature-data",
      },
    };

    // should verify the profile
    let res = await request(app)
      .post('/v1/user/profileVerificationPicture/liveness')
      .set('authorization', 1)
      .send(payload);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();

    // get first picture hidden - keep verification
    setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0]}');

    res = await request(app)
      .post('/v1/report')
      .set('authorization', 0)
      .send({
        user: '1',
        reason: ['Inappropriate Profile'],
      });
    expect(res.status).to.equal(200);

    redactedPictures = [redactedPictures[1]];

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 1)
      .send({ appVersion: '1.13.0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.pictures).to.eql(originalPictures);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
    expect(res.body.user.rejectionReason).to.equal();

    res = await request(app)
      .get('/v1/user/profile')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.body.user.pictures).to.eql(redactedPictures);
    expect(res.body.user.verified).to.equal(true);
    expect(res.body.user.verificationStatus).to.equal('verified');
  });
});

it('infringing pictures - ignore videos', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);

  // picture, then video, then picture
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/profileVideo')
    .set('authorization', 1)
    .attach('video', validVideoPath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  originalPictures = res.body.pictures;
  redactedPictures = [ originalPictures[0], originalPictures[1] ];

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [1]}');

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find().sort('-createdAt');
  expect(reports.length).to.equal(2);
  console.log(reports[0]);
  //expect(reports[0].status).to.equal('verified');
  expect(reports[0].openai.ban).to.equal(true);
  expect(reports[0].openai.banReason).to.equal('Spam, Promotion or Solicitation');
  expect(reports[0].openai.violationLocation).to.equal('profile');
  expect(reports[0].openai.decision).to.equal('shadow_hide');
  expect(reports[0].openai.infringingText).to.eql();
  expect(reports[0].openai.infringingPictures).to.eql([1]);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.pictures).to.eql(originalPictures);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(redactedPictures);
});

it('infringing pictures - reorder if video in first position', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);

  // picture, then video, then picture
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/profileVideo')
    .set('authorization', 1)
    .attach('video', validVideoPath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  originalPictures = res.body.pictures;
  redactedPictures = [ originalPictures[2], originalPictures[1] ];

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0]}');

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.pictures).to.eql(originalPictures);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(redactedPictures);
});

it('infringing pictures - ban if no non-hidden pictures remaining', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);

  // picture, then video
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/profileVideo')
    .set('authorization', 1)
    .attach('video', validVideoPath);
  expect(res.status).to.equal(200);
  originalPictures = res.body.pictures;

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [0]}');

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('All pictures shadow hidden');

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.pictures).to.eql(originalPictures);
});

it('infringing pictures - reorder if video in first position after reorder', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);

  // picture, then video, then picture
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/profileVideo')
    .set('authorization', 1)
    .attach('video', validVideoPath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  originalPictures = res.body.pictures;
  redactedPictures = [ originalPictures[0], originalPictures[1] ];

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [1]}');

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.pictures).to.eql(originalPictures);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(redactedPictures);

  // reorder
  originalPictures = [originalPictures[2], originalPictures[1], originalPictures[0]];
  res = await request(app)
    .put('/v1/user/reorderPictures')
    .set('authorization', 1)
    .send({ ids: originalPictures })
  expect(res.status).to.equal(200);
  expect(res.body).to.eql(originalPictures);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(originalPictures);

  // other user should still see the video in the second position
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(redactedPictures);
});

it('infringing pictures - only video remaining after delete picture - ban', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);

  // picture, then video, then picture
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/profileVideo')
    .set('authorization', 1)
    .attach('video', validVideoPath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  originalPictures = res.body.pictures;
  redactedPictures = [ originalPictures[0], originalPictures[1] ];

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": true, "reason": "spam", "violationLocation": "profile", "infringingText": [], "infringingPictures": [1]}');

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(false);

  // user should see the original version
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal();
  expect(res.body.user.pictures).to.eql(originalPictures);

  // other user should see the redacted version
  res = await request(app)
    .get('/v1/user/profile')
    .set('authorization', 0)
    .query({ user: '1' });
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(redactedPictures);

  // delete non-hidden picture
  res = await request(app)
    .delete('/v1/user/picture')
    .set('authorization', 1)
    .query({ id: originalPictures[0] })
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  originalPictures = [originalPictures[1], originalPictures[2]];

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);
  expect(res.body.user.pictures).to.eql(originalPictures);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('All pictures shadow hidden');
});

it('infringing pictures, should add profileTempBanInfringingText, profileTempBanInfringingPictures', async () => {
  setMockPromptResponse('{"ban": true, "reason": "spam"}');

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);

  // picture, then video, then picture
  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', 1)
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);
  originalPictures = res.body.pictures;

  // report
  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  setMockPromptResponse('{"ban": true, "reason": "rude", "violationLocation": "profile", "infringingText": ["mock", "keyword"], "infringingPictures": [1]}');

  socket = await initSocket('1');
  socketPromise = getSocketPromise(socket, 'profileTempBan');

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('Meanness or Rudeness');
  expect(user.profileTempBanInfringingText).to.eql(['mock', 'keyword']);
  expect(user.profileTempBanInfringingPictures).to.eql([originalPictures[1]]);

  await waitMs(100);
  res = await socketPromise;
  expect(res).to.eql({
    profileTempBanReason: 'Meanness or Rudeness',
    profileTempBanInfringingText: ['mock', 'keyword'],
    profileTempBanInfringingPictures: [originalPictures[1]],
  });

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
    .send({ appVersion: '1.13.0' })
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Meanness or Rudeness');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['mock', 'keyword']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql([originalPictures[1]]);

  await destroySocket(socket);
});

it('notify colorado matches when user is banned', async () => {
  emailStub = sinon.stub(ses, 'sendEmail')
    .callsFake((params) => {
      console.log('Fake sendEmail', JSON.stringify(params));
      const impl = function (resolve, reject) {
        resolve({});
      };
      return {
        promise: () => new Promise(impl),
      };
    });

  // user 0 to be banned
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 0)
    .send({
      latitude: 39.73,
      longitude: -104.99,
    });
  expect(res.status).to.equal(200);

  // user 1 with location Colorado
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 1)
    .send({
      latitude: 39.73,
      longitude: -104.99,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.actualState).to.equal('Colorado');

  // user 2 with IP Colorado
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
  expect(res.status).to.equal(200);

  // user 3 with location California
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 3)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 3)
    .send({
      latitude: 34.05,
      longitude: -118.24,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('3');
  expect(user.actualState).to.equal('California');

  // user 4 with location Colorado who unmatched
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 4)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 4)
    .send({
      latitude: 39.73,
      longitude: -104.99,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('4');
  expect(user.actualState).to.equal('Colorado');

  // user 5 with location Colorado who sent like to user 0 but not matched
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 5)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 5)
    .send({
      latitude: 39.73,
      longitude: -104.99,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('5');
  expect(user.actualState).to.equal('Colorado');

  // user 6 with location Colorado but no interaction with user 0
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 6)
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', 6)
    .send({
      latitude: 39.73,
      longitude: -104.99,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('6');
  expect(user.actualState).to.equal('Colorado');

  // set up user names
  for (let i = 0; i < 7; i++) {
    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', i)
      .send({ firstName: `name${i}` });
    expect(res.status).to.equal(200);
  }

  // set up interactions
  for (let i = 1; i < 5; i++) {
    res = await request(app)
      .patch('/v1/user/sendLike')
      .set('authorization', i)
      .send({
        user: '0',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .patch('/v1/user/approve')
      .set('authorization', 0)
      .send({
        user: i.toString(),
      });
    expect(res.status).to.equal(200);
  }

  res = await request(app)
    .patch('/v1/user/unmatch')
    .set('authorization', 4)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  res = await request(app)
    .patch('/v1/user/sendLike')
    .set('authorization', 5)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  // set IP Colorado for user 2
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 2)
    .set('X-Forwarded-For', '************')
  expect(res.status).to.equal(200);

  user = await User.findById('2');
  console.log(user.ipData);
  expect(user.ipData.region).to.equal('CO');
  expect(user.ipData.countryCode).to.equal('US');

  // set up admin user
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 100)
  expect(res.status).to.equal(200);

  user = await User.findOne({ _id: 100 });
  user.admin = true;
  user.adminPermissions = { all: true };
  await user.save();

  // ban user 0 with reason Inappropriate Profile - no emails sent
  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 100)
    .send({
      user: '0',
      bannedReason: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  assert(emailStub.notCalled);

  // unban user 0
  res = await request(app)
    .put('/v1/admin/unban')
    .set('authorization', 100)
    .send({
      user: '0',
    });
  expect(res.status).to.equal(200);

  // ban user 0 with reason Spam - emails sent
  res = await request(app)
    .put('/v1/admin/ban')
    .set('authorization', 100)
    .send({
      user: '0',
      bannedReason: 'Spam',
    });
  expect(res.status).to.equal(200);

  expect(emailStub.callCount).to.equal(2);
  expect(emailStub.getCall(0).args[0].Destination.ToAddresses).to.eql(['<EMAIL>']);
  expect(emailStub.getCall(0).args[0].Message.Body.Text.Data).to.equal('Hi name2,\n\nThis email is notify you that one of your matches, name0, has been banned for fraudulent behavior. Please exercise caution when exchanging information, and please review our Safety Tips.\n\nLove,\nBoo');
  expect(emailStub.getCall(0).args[0].Message.Subject.Data).to.equal('Safety Alert');
  expect(emailStub.getCall(0).args[0].Source).to.equal('Boo <<EMAIL>>');

  expect(emailStub.getCall(1).args[0].Destination.ToAddresses).to.eql(['<EMAIL>']);
  expect(emailStub.getCall(1).args[0].Message.Body.Text.Data).to.equal('Hi name1,\n\nThis email is notify you that one of your matches, name0, has been banned for fraudulent behavior. Please exercise caution when exchanging information, and please review our Safety Tips.\n\nLove,\nBoo');
  expect(emailStub.getCall(1).args[0].Message.Subject.Data).to.equal('Safety Alert');
  expect(emailStub.getCall(1).args[0].Source).to.equal('Boo <<EMAIL>>');
});

it('empty reason should not error', async () => {

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 0);
  expect(res.status).to.equal(200);
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', 1);
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 0)
    .send({
      user: '1',
      reason: [],
    });
  expect(res.status).to.equal(200);

  const reports = await Report.find();
  expect(reports.length).to.equal(1);
  console.log(reports[0]);
  expect(reports[0].reason).to.eql([]);
});

/* Disabled soft ban
it('app_657_v3 soft ban unverified users', async () => {
  constants.hideUnverifiedUsers.restore();
  sinon.stub(constants, 'hideUnverifiedUsers').returns(true);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '0')
    .send({ os: 'android', deviceId: 'device0', appVersion: '1.13.77' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', '0')
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', '0')
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.viewableInDailyProfiles).to.equal(false);
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('soft ban for unverified users');

  // banned device id logic should be skipped
  constants.hideUnverifiedUsers.restore();
  sinon.stub(constants, 'hideUnverifiedUsers').returns(false);

  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '100')
    .send({ os: 'android', deviceId: 'device0' });
  expect(res.status).to.equal(200);

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', '100')
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app)
    .put('/v1/user/location')
    .set('authorization', '100')
    .send({
      latitude: 21.30,
      longitude: -157.85,
    });
  expect(res.status).to.equal(200);

  user = await User.findById('100');
  expect(user.viewableInDailyProfiles).to.equal(true);
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();

  // user should be unbanned after verifying
  const payload = {
    img: 'base-64-image-data',
    secure: {
      version: "2.7.0",
      token: "token-data",
      verification: "verification-data",
      signature: "signature-data",
    },
  };

  res = await request(app)
    .post('/v1/user/profileVerificationPicture/liveness')
    .set('authorization', 0)
    .send(payload);
  expect(res.status).to.equal(200);
  expect(res.body.verificationStatus).to.equal('verified');
  expect(res.body.rejectionReason).to.equal(undefined);

  await new Promise((r) => setTimeout(r, 200));

  user = await User.findById('0');
  expect(user.viewableInDailyProfiles).to.equal(true);
  expect(user.shadowBanned).to.equal(false);
  expect(user.bannedReason).to.equal();
});
*/
it('handle new signups on old versions', async () => {
  constants.requireManualVerificationForWeb.restore();
  sinon.stub(constants, 'requireManualVerificationForWeb').returns(true);

  // version 1.13.46 shadow banned
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '0')
    .send({ os: 'ios', appVersion: '1.13.46' });
  expect(res.status).to.equal(200);

  user = await User.findById('0');
  expect(user.viewableInDailyProfiles).to.equal(false);
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('new signup on old version');
  expect(user.bannedNotes).to.equal('1.13.46');

  res = await request(app)
    .post('/v1/user/picture/v2')
    .set('authorization', '0')
    .attach('image', validImagePath);
  expect(res.status).to.equal(200);

  const payload = {
    img: 'base-64-image-data',
    secure: {
      version: "2.7.0",
      token: "token-data",
      verification: "verification-data",
      signature: "signature-data",
    },
  };

  // verification not auto rejected
  res = await request(app)
    .post('/v1/user/profileVerificationPicture/liveness')
    .set('authorization', 0)
    .send(payload);
  expect(res.status).to.equal(200);
  expect(res.body.verificationStatus).to.equal('pending');
});

it('renameBannedReasonForPhotoVerificationFirstReview', async () => {

  const testdata = [
    // original ban reason, renamed ban reason
    ['Scammer (banned during photo verification first review)', 'Scammer'],
    ['Underage (banned during photo verification first review)', 'Underage'],
    ['Scammer', 'Scammer'],
    ['Other', 'Other'],
  ];

  for (let i = 0; i < testdata.length; i++) {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);

    user = await User.findById(i.toString());
    user.shadowBanned = true;
    user.bannedReason = testdata[i][0];
    await user.save();
  }

  await reportLib.renameBannedReasonForPhotoVerificationFirstReview();

  for (let i = 0; i < testdata.length; i++) {
    user = await User.findById(i.toString());
    user.shadowBanned = true;
    user.bannedReason = testdata[i][1];
    await user.save();
  }
});

it('Replace shadow ban for infringing name with temp shadow ban', async () => {
  for (let i = 0; i < 4; i++) {
    let res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', i);
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/firstName')
      .set('authorization', i)
      .send({
        firstName: `name${i}`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/education')
      .set('authorization', i)
      .send({
        education: `education${i}`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/description')
      .set('authorization', i)
      .send({
        description: `description${i}`,
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/work')
      .set('authorization', i)
      .send({
        work: `work${i}`,
      });
    expect(res.status).to.equal(200);
  }

  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Spam, Promotion or Solicitation',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: ['name', 'work', 'description'],
  }));

  let res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '0',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  let user = await User.findById('0');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.profileTempBanReason).to.equal('Spam, Promotion or Solicitation');
  expect(user.profileTempBanInfringingText).to.eql(['name', 'work', 'description']);
  expect(user.profileTempBanInfringingPictures).to.eql();

  // condition setup for existing bans
  setMockPromptResponse(JSON.stringify({
    ban: true,
    reason: 'Scamming',
    explanation: 'The profile contains a social media handle, which is a violation of community guidelines.',
    infringingText: ['testN'],
  }));

  res = await request(app)
    .post('/v1/report')
    .set('authorization', 2)
    .send({
      user: '1',
      reason: ['Inappropriate Profile'],
      comment: 'Inappropriate Profile',
    });
  expect(res.status).to.equal(200);

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  user.firstName = 'testName';
  user.bannedReason = 'infringing text found in name';
  await user.save();

  let report = await Report.findOne({ reportedUser: '1' });
  report.openai.decision = 'shadow_hide';
  report.openai.infringingTextFoundInName = true;
  await report.save();

  // initialize user 1 again
  res = await request(app)
    .put('/v1/user/initApp')
    .set('authorization', '1');
  expect(res.status).to.equal(200);
  expect(res.body.user.profileTempBanReason).to.equal('Scamming');
  expect(res.body.user.profileTempBanInfringingText).to.eql(['testN']);
  expect(res.body.user.profileTempBanInfringingPictures).to.eql();

  user = await User.findById('1');
  expect(user.shadowBanned).to.equal(true);
  expect(user.bannedReason).to.equal('temp shadow ban due to inappropriate profile');
  expect(user.firstName).to.equal('testName');
  expect(user.profileTempBanReason).to.equal('Scamming');
  expect(user.profileTempBanInfringingText).to.eql(['testN']);
  expect(user.profileTempBanInfringingPictures).to.eql();
});
